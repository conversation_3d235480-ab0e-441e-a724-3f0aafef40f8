# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
dist
tmp
/out-tsc
/data

# dependencies
node_modules

# IDEs and editors
/.idea
/.fleet
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

/env/.current_env
/env/.current_profile
.env
/env/.*.override
/.envrc.override

packages/dev/database/bin/cli-hasura-*
/tools/bin/.download
/tools/bin/cli_temporal*
/tools/bin/cli_nats_*

packages/libs/intent-resolver/assets/*.api-schema-ts

.nx/cache
