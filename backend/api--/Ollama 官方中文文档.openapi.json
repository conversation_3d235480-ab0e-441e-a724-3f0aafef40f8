{"openapi": "3.0.1", "paths": {"/api/generate": {"post": {"summary": "卸载模型", "deprecated": false, "description": "If an empty prompt is provided and the `keep_alive` parameter is set to `0`, a model will be unloaded from memory.", "tags": ["生成补全"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string", "enum": ["llama3.2"], "examples": ["llama3.2"]}, "keep_alive": {"type": "integer", "minimum": 0}}, "required": ["model", "keep_alive"]}}}}, "responses": {"200": {"description": "A single JSON object is returned:", "content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string"}, "created_at": {"type": "string"}, "response": {"type": "string"}, "done": {"type": "boolean"}, "done_reason": {"type": "string"}}, "required": ["model", "created_at", "response", "done", "done_reason"]}, "example": {"model": "llama3.2", "created_at": "2024-09-12T03:54:03.516566Z", "response": "", "done": true, "done_reason": "unload"}}}, "headers": {}}}, "security": []}}, "/api/chat": {"post": {"summary": "卸载模型", "deprecated": false, "description": "If the messages array is empty and the `keep_alive` parameter is set to `0`, a model will be unloaded from memory.", "tags": ["生成对话补全"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string", "enum": ["llama3.2"], "example": "llama3.2"}, "messages": {"type": "array", "items": {"type": "object", "example": {"role": "user", "content": "你好"}, "properties": {}}}, "keep_alive": {"type": "integer", "minimum": 0, "example": 60}}, "required": ["model", "messages", "keep_alive"], "example": {"model": "llama3.2", "messages": [{"role": "user", "content": "你好"}], "keep_alive": 60}}, "examples": {}}}}, "responses": {"200": {"description": "A single JSON object is returned:", "content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string"}, "created_at": {"type": "string"}, "message": {"type": "object", "properties": {"role": {"type": "string"}, "content": {"type": "string"}}, "required": ["role", "content"]}, "done_reason": {"type": "string"}, "done": {"type": "boolean"}}, "required": ["model", "created_at", "message", "done_reason", "done"]}, "example": {"model": "llama3.2", "created_at": "2024-09-12T21:33:17.547535Z", "message": {"role": "assistant", "content": ""}, "done_reason": "unload", "done": true}}}, "headers": {}}}, "security": []}}, "/api/tags": {"get": {"summary": "示例", "deprecated": false, "description": "", "tags": ["列出本地模型"], "parameters": [], "responses": {"200": {"description": "A single JSON object will be returned.", "content": {"application/json": {"schema": {"type": "object", "properties": {"models": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "modified_at": {"type": "string"}, "size": {"type": "integer"}, "digest": {"type": "string"}, "details": {"type": "object", "properties": {"format": {"type": "string"}, "family": {"type": "string"}, "families": {"type": "null"}, "parameter_size": {"type": "string"}, "quantization_level": {"type": "string"}}, "required": ["format", "family", "families", "parameter_size", "quantization_level"]}}, "required": ["name", "modified_at", "size", "digest", "details"]}}}, "required": ["models"]}, "example": {"models": [{"name": "codellama:13b", "modified_at": "2023-11-04T14:56:49.277302595-07:00", "size": 7365960935, "digest": "9f438cb9cd581fc025612d27f7c1a6669ff83a8bb0ed86c94fcf4c5440555697", "details": {"format": "gguf", "family": "llama", "families": null, "parameter_size": "13B", "quantization_level": "Q4_0"}}, {"name": "llama3:latest", "modified_at": "2023-12-07T09:32:18.757212583-08:00", "size": 3825819519, "digest": "fe938a131f40e6f6d40083c9f0f430a515233eb2edaa6d72eb85c50d64f2300e", "details": {"format": "gguf", "family": "llama", "families": null, "parameter_size": "7B", "quantization_level": "Q4_0"}}]}}}, "headers": {}}}, "security": []}}, "/api/show": {"post": {"summary": "示例", "deprecated": false, "description": "", "tags": ["显示模型详情"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string", "examples": ["llama3.2"]}}, "required": ["model"]}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"modelfile": {"type": "string"}, "parameters": {"type": "string"}, "template": {"type": "string"}, "details": {"type": "object", "properties": {"parent_model": {"type": "string"}, "format": {"type": "string"}, "family": {"type": "string"}, "families": {"type": "array", "items": {"type": "string"}}, "parameter_size": {"type": "string"}, "quantization_level": {"type": "string"}}, "required": ["parent_model", "format", "family", "families", "parameter_size", "quantization_level"]}, "model_info": {"type": "object", "properties": {"general.architecture": {"type": "string"}, "general.file_type": {"type": "integer"}, "general.parameter_count": {"type": "integer"}, "general.quantization_version": {"type": "integer"}, "llama.attention.head_count": {"type": "integer"}, "llama.attention.head_count_kv": {"type": "integer"}, "llama.attention.layer_norm_rms_epsilon": {"type": "number"}, "llama.block_count": {"type": "integer"}, "llama.context_length": {"type": "integer"}, "llama.embedding_length": {"type": "integer"}, "llama.feed_forward_length": {"type": "integer"}, "llama.rope.dimension_count": {"type": "integer"}, "llama.rope.freq_base": {"type": "integer"}, "llama.vocab_size": {"type": "integer"}, "tokenizer.ggml.bos_token_id": {"type": "integer"}, "tokenizer.ggml.eos_token_id": {"type": "integer"}, "tokenizer.ggml.merges": {"type": "array", "items": {"type": "string"}, "description": "populates if `verbose=true`"}, "tokenizer.ggml.model": {"type": "string"}, "tokenizer.ggml.pre": {"type": "string"}, "tokenizer.ggml.token_type": {"type": "array", "items": {"type": "string"}, "description": "populates if `verbose=true`"}, "tokenizer.ggml.tokens": {"type": "array", "items": {"type": "string"}, "description": "populates if `verbose=true`"}}, "required": ["general.architecture", "general.file_type", "general.parameter_count", "general.quantization_version", "llama.attention.head_count", "llama.attention.head_count_kv", "llama.attention.layer_norm_rms_epsilon", "llama.block_count", "llama.context_length", "llama.embedding_length", "llama.feed_forward_length", "llama.rope.dimension_count", "llama.rope.freq_base", "llama.vocab_size", "tokenizer.ggml.bos_token_id", "tokenizer.ggml.eos_token_id", "tokenizer.ggml.merges", "tokenizer.ggml.model", "tokenizer.ggml.pre", "tokenizer.ggml.token_type", "tokenizer.ggml.tokens"]}}, "required": ["modelfile", "parameters", "template", "details", "model_info"]}, "example": {"modelfile": "# Modelfile generated by \"ollama show\"\n# To build a new Modelfile based on this one, replace the FROM line with:\n# FROM llava:latest\n\nFROM /Users/<USER>/.ollama/models/blobs/sha256:200765e1283640ffbd013184bf496e261032fa75b99498a9613be4e94d63ad52\nTEMPLATE \"\"\"{{ .System }}\nUSER: {{ .Prompt }}\nASSISTANT: \"\"\"\nPARAMETER num_ctx 4096\nPARAMETER stop \"</s>\"\nPARAMETER stop \"USER:\"\nPARAMETER stop \"ASSISTANT:\"", "parameters": "num_keep                       24\nstop                           \"<|start_header_id|>\"\nstop                           \"<|end_header_id|>\"\nstop                           \"<|eot_id|>\"", "template": "{{ if .System }}<|start_header_id|>system<|end_header_id|>\n\n{{ .System }}<|eot_id|>{{ end }}{{ if .Prompt }}<|start_header_id|>user<|end_header_id|>\n\n{{ .Prompt }}<|eot_id|>{{ end }}<|start_header_id|>assistant<|end_header_id|>\n\n{{ .Response }}<|eot_id|>", "details": {"parent_model": "", "format": "gguf", "family": "llama", "families": ["llama"], "parameter_size": "8.0B", "quantization_level": "Q4_0"}, "model_info": {"general.architecture": "llama", "general.file_type": 2, "general.parameter_count": 8030261248, "general.quantization_version": 2, "llama.attention.head_count": 32, "llama.attention.head_count_kv": 8, "llama.attention.layer_norm_rms_epsilon": 1e-05, "llama.block_count": 32, "llama.context_length": 8192, "llama.embedding_length": 4096, "llama.feed_forward_length": 14336, "llama.rope.dimension_count": 128, "llama.rope.freq_base": 500000, "llama.vocab_size": 128256, "tokenizer.ggml.bos_token_id": 128000, "tokenizer.ggml.eos_token_id": 128009, "tokenizer.ggml.merges": [], "tokenizer.ggml.model": "gpt2", "tokenizer.ggml.pre": "llama-bpe", "tokenizer.ggml.token_type": [], "tokenizer.ggml.tokens": []}}}}, "headers": {}}}, "security": []}}, "/api/embed": {"post": {"summary": "多输入请求（Multiple Input）", "deprecated": false, "description": "", "tags": ["生成嵌入向量"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string", "description": "使用的模型名称", "example": "gpt-4"}, "input": {"type": "array", "items": {"type": "string"}, "description": "输入的查询问题列表", "example": ["今天天气怎么样？", "北京现在的温度是多少？"]}}, "required": ["model", "input"]}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string"}, "embeddings": {"type": "array", "items": {"type": "array", "items": {"type": "number"}}}}, "required": ["model", "embeddings"]}, "example": {"model": "all-minilm", "embeddings": [[0.010071029, -0.0017594862, 0.05007221, 0.04692972, 0.054916814, 0.008599704, 0.105441414, -0.025878139, 0.12958129, 0.031952348], [-0.0098027075, 0.06042469, 0.025257962, -0.006364387, 0.07272725, 0.017194884, 0.09032035, -0.051705178, 0.09951512, 0.09072481]]}}}, "headers": {}}}, "security": []}}, "/api/ps": {"post": {"summary": "示例", "deprecated": false, "description": "", "tags": ["列出运行中模型"], "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"models": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "model": {"type": "string"}, "size": {"type": "integer"}, "digest": {"type": "string"}, "details": {"type": "object", "properties": {"parent_model": {"type": "string"}, "format": {"type": "string"}, "family": {"type": "string"}, "families": {"type": "array", "items": {"type": "string"}}, "parameter_size": {"type": "string"}, "quantization_level": {"type": "string"}}, "required": ["parent_model", "format", "family", "families", "parameter_size", "quantization_level"]}, "expires_at": {"type": "string"}, "size_vram": {"type": "integer"}}}}}, "required": ["models"]}, "example": {"models": [{"name": "mistral:latest", "model": "mistral:latest", "size": 5137025024, "digest": "2ae6f6dd7a3dd734790bbbf58b8909a606e0e7e97e94b7604e0aa7ae4490e6d8", "details": {"parent_model": "", "format": "gguf", "family": "llama", "families": ["llama"], "parameter_size": "7.2B", "quantization_level": "Q4_0"}, "expires_at": "2024-06-04T14:38:31.83753-07:00", "size_vram": 5137025024}]}}}, "headers": {}}}, "security": []}}, "/api/embeddings": {"post": {"summary": "示例", "deprecated": false, "description": "", "tags": ["生成单个嵌入向量"], "parameters": [], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string", "description": "使用的模型名称", "example": "all-minilm"}, "prompt": {"type": "string", "description": "输入提示文本", "example": "这是一篇关于羊驼的文章..."}}, "required": ["model", "prompt"]}, "examples": {}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"embedding": {"type": "array", "items": {"type": "number"}}}, "required": ["embedding"]}, "example": {"embedding": [0.5670403838157654, 0.009260174818336964, 0.23178744316101074, -0.2916173040866852, -0.8924556970596313, 0.8785552978515625, -0.34576427936553955, 0.5742510557174683, -0.04222835972905159, -0.137906014919281]}}}, "headers": {}}}, "security": []}}}}