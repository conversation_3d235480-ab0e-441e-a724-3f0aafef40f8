{"openapi": "3.0.1", "paths": {"/v1/chat/completions": {"post": {"summary": "创建 Chat Completions", "deprecated": false, "description": "给定一个提示，该模型将返回一个或多个预测的完成，并且还可以返回每个位置的替代标记的概率。\n\n为提供的提示和参数创建完成\n\n", "tags": ["聊天（Chat）"], "parameters": [{"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}, {"name": "Accept", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer {{YOUR_API_KEY}}", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string", "description": "要使用的模型的 ID。有关哪些模型可与聊天 API 一起使用的详细信息,请参阅模型端点兼容性表。\n\n"}, "messages": {"type": "array", "items": {"type": "object", "properties": {"role": {"type": "string"}, "content": {"type": "string"}}}, "description": "至今为止对话所包含的消息列表。Python 代码示例。"}, "temperature": {"type": "integer", "description": "使用什么采样温度，介于 0 和 2 之间。较高的值（如 0.8）将使输出更加随机，而较低的值（如 0.2）将使输出更加集中和确定。  我们通常建议改变这个或`top_p`但不是两者。"}, "top_p": {"type": "integer", "description": "一种替代温度采样的方法，称为核采样，其中模型考虑具有 top_p 概率质量的标记的结果。所以 0.1 意味着只考虑构成前 10% 概率质量的标记。  我们通常建议改变这个或`temperature`但不是两者。"}, "n": {"type": "integer", "description": "默认为 1\n为每个输入消息生成多少个聊天补全选择。"}, "stream": {"type": "boolean", "description": "默认为 false 如果设置,则像在 ChatGPT 中一样会发送部分消息增量。标记将以仅数据的服务器发送事件的形式发送,这些事件在可用时,并在 data: [DONE] 消息终止流。Python 代码示例。"}, "stop": {"type": "string", "description": "默认为 null 最多 4 个序列,API 将停止进一步生成标记。"}, "max_tokens": {"type": "integer", "description": "默认为 inf\n在聊天补全中生成的最大标记数。\n\n输入标记和生成标记的总长度受模型的上下文长度限制。计算标记的 Python 代码示例。"}, "presence_penalty": {"type": "number", "description": "-2.0 和 2.0 之间的数字。正值会根据到目前为止是否出现在文本中来惩罚新标记，从而增加模型谈论新主题的可能性。  [查看有关频率和存在惩罚的更多信息。](https://platform.openai.com/docs/api-reference/parameter-details)"}, "frequency_penalty": {"type": "number", "description": "默认为 0 -2.0 到 2.0 之间的数字。正值根据文本目前的存在频率惩罚新标记,降低模型重复相同行的可能性。  有关频率和存在惩罚的更多信息。"}, "logit_bias": {"type": "null", "description": "修改指定标记出现在补全中的可能性。\n\n接受一个 JSON 对象,该对象将标记(由标记器指定的标记 ID)映射到相关的偏差值(-100 到 100)。从数学上讲,偏差在对模型进行采样之前添加到模型生成的 logit 中。确切效果因模型而异,但-1 和 1 之间的值应减少或增加相关标记的选择可能性;如-100 或 100 这样的值应导致相关标记的禁用或独占选择。"}, "user": {"type": "string", "description": "代表您的最终用户的唯一标识符，可以帮助 OpenAI 监控和检测滥用行为。[了解更多](https://platform.openai.com/docs/guides/safety-best-practices/end-user-ids)。"}, "response_format": {"type": "object", "properties": {}, "description": "指定模型必须输出的格式的对象。  将 { \"type\": \"json_object\" } 启用 JSON 模式,这可以确保模型生成的消息是有效的 JSON。  重要提示:使用 JSON 模式时,还必须通过系统或用户消息指示模型生成 JSON。如果不这样做,模型可能会生成无休止的空白流,直到生成达到令牌限制,从而导致延迟增加和请求“卡住”的外观。另请注意,如果 finish_reason=\"length\",则消息内容可能会被部分切断,这表示生成超过了 max_tokens 或对话超过了最大上下文长度。  显示属性"}, "seen": {"type": "integer", "description": "此功能处于测试阶段。如果指定,我们的系统将尽最大努力确定性地进行采样,以便使用相同的种子和参数进行重复请求应返回相同的结果。不能保证确定性,您应该参考 system_fingerprint 响应参数来监控后端的更改。"}, "tools": {"type": "array", "items": {"type": "string"}, "description": "模型可以调用的一组工具列表。目前,只支持作为工具的函数。使用此功能来提供模型可以为之生成 JSON 输入的函数列表。"}, "tool_choice": {"type": "object", "properties": {}, "description": "控制模型调用哪个函数(如果有的话)。none 表示模型不会调用函数,而是生成消息。auto 表示模型可以在生成消息和调用函数之间进行选择。通过 {\"type\": \"function\", \"function\": {\"name\": \"my_function\"}} 强制模型调用该函数。  如果没有函数存在,默认为 none。如果有函数存在,默认为 auto。  显示可能的类型"}}, "required": ["model", "messages", "tools", "tool_choice"]}, "example": {"model": "gpt-3.5-turbo", "messages": [{"role": "system", "content": "You are a helpful assistant."}, {"role": "user", "content": "Hello!"}]}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string"}, "created": {"type": "integer"}, "choices": {"type": "array", "items": {"type": "object", "properties": {"index": {"type": "integer"}, "message": {"type": "object", "properties": {"role": {"type": "string"}, "content": {"type": "string"}}, "required": ["role", "content"]}, "finish_reason": {"type": "string"}}}}, "usage": {"type": "object", "properties": {"prompt_tokens": {"type": "integer"}, "completion_tokens": {"type": "integer"}, "total_tokens": {"type": "integer"}}, "required": ["prompt_tokens", "completion_tokens", "total_tokens"]}}, "required": ["id", "object", "created", "choices", "usage"]}, "example": {"id": "chatcmpl-123", "object": "chat.completion", "created": 1677652288, "choices": [{"index": 0, "message": {"role": "assistant", "content": "\n\nHello there, how may I assist you today?"}, "finish_reason": "stop"}], "usage": {"prompt_tokens": 9, "completion_tokens": 12, "total_tokens": 21}}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/v1/completions": {"post": {"summary": "创建 Completions", "deprecated": false, "description": "给定一个提示，该模型将返回一个或多个预测的完成，并且还可以返回每个位置的替代标记的概率。\n\n为提供的提示和参数创建完成\n\n", "tags": ["自动补全（Completions）"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer {{YOUR_API_KEY}}", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string", "title": "", "description": "要使用的模型的 ID。您可以使用[List models](https://platform.openai.com/docs/api-reference/models/list) API 来查看所有可用模型，或查看我们的[模型概述](https://platform.openai.com/docs/models/overview)以了解它们的描述。"}, "prompt": {"type": "string", "title": "", "description": "生成完成的提示，编码为字符串、字符串数组、标记数组或标记数组数组。  请注意，<|endoftext|> 是模型在训练期间看到的文档分隔符，因此如果未指定提示，模型将生成新文档的开头。"}, "best_of": {"type": "integer", "description": "默认为1 在服务器端生成best_of个补全,并返回“最佳”补全(每个令牌的日志概率最高的那个)。无法流式传输结果。  与n一起使用时,best_of控制候选补全的数量,n指定要返回的数量 – best_of必须大于n。  注意:因为这个参数会生成许多补全,所以它可以快速消耗您的令牌配额。请谨慎使用,并确保您对max_tokens和stop有合理的设置。"}, "echo": {"type": "boolean", "description": "默认为false 除了补全之外,还回显提示"}, "frequency_penalty": {"type": "number", "description": "默认为0 -2.0和2.0之间的数字。正值根据文本目前的现有频率处罚新令牌,降低模型逐字重复相同行的可能性。"}, "logit_bias": {"type": "object", "properties": {}, "description": "默认为null 修改完成中指定令牌出现的可能性。  接受一个JSON对象,该对象将令牌(由GPT令牌化器中的令牌ID指定)映射到关联偏差值,-100到100。您可以使用这个令牌化器工具(适用于GPT-2和GPT-3)将文本转换为令牌ID。从数学上讲,偏差在对模型进行采样之前添加到生成的logit中。确切效果因模型而异,但-1至1之间的值应降低或提高选择的可能性;像-100或100这样的值应导致相关令牌的禁用或专属选择。  例如,您可以传递{\"50256\": -100}来防止生成<|endoftext|>令牌。"}, "logprobs": {"type": "null", "title": "", "description": "默认为null\n包括logprobs个最可能令牌的日志概率,以及所选令牌。例如,如果logprobs为5,API将返回5个最有可能令牌的列表。 API总会返回采样令牌的logprob,因此响应中最多可能有logprobs+1个元素。\n\nlogprobs的最大值是5。"}, "max_tokens": {"type": "integer", "title": "", "description": "默认为16\n在补全中生成的最大令牌数。\n\n提示的令牌计数加上max_tokens不能超过模型的上下文长度。 计数令牌的Python代码示例。"}, "n": {"type": "integer", "description": "默认为1\n为每个提示生成的补全数量。\n\n注意:因为这个参数会生成许多补全,所以它可以快速消耗您的令牌配额。请谨慎使用,并确保您对max_tokens和stop有合理的设置。"}, "presence_penalty": {"type": "number", "description": "默认为0 -2.0和2.0之间的数字。正值根据它们是否出现在目前的文本中来惩罚新令牌,增加模型讨论新话题的可能性。  有关频率和存在惩罚的更多信息,请参阅。"}, "seed": {"type": "integer", "description": "如果指定,我们的系统将尽最大努力确定性地进行采样,以便使用相同的种子和参数的重复请求应返回相同的结果。  不保证确定性,您应该参考system_fingerprint响应参数来监视后端的更改。"}, "stop": {"type": "string", "title": "", "description": "默认为null 最多4个序列,API将停止在其中生成更多令牌。返回的文本不会包含停止序列。"}, "stream": {"type": "boolean", "title": "", "description": "默认为false 是否流回部分进度。如果设置,令牌将作为可用时发送为仅数据的服务器发送事件,流由数据 Terminated by a data: [DONE] message. 对象消息终止。 Python代码示例。"}, "suffix": {"type": "string", "description": "默认为null 在插入文本的补全之后出现的后缀。"}, "temperature": {"type": "integer", "title": "", "description": "默认为1 要使用的采样温度,介于0和2之间。更高的值(如0.8)将使输出更随机,而更低的值(如0.2)将使其更集中和确定。  我们通常建议更改这个或top_p,而不是两者都更改。"}, "user": {"type": "string"}, "top_p": {"type": "integer", "description": "表示最终用户的唯一标识符,这可以帮助OpenAI监控和检测滥用。 了解更多。"}}, "required": ["model", "prompt", "user"]}, "example": {"model": "gpt-3.5-turbo-instruct", "prompt": "Say this is a test", "max_tokens": 7, "temperature": 0}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string"}, "created": {"type": "integer"}, "model": {"type": "string"}, "system_fingerprint": {"type": "string"}, "choices": {"type": "array", "items": {"type": "object", "properties": {"text": {"type": "string"}, "index": {"type": "integer"}, "logprobs": {"type": "null"}, "finish_reason": {"type": "string"}}}}, "usage": {"type": "object", "properties": {"prompt_tokens": {"type": "integer"}, "completion_tokens": {"type": "integer"}, "total_tokens": {"type": "integer"}}, "required": ["prompt_tokens", "completion_tokens", "total_tokens"]}}, "required": ["id", "object", "created", "model", "system_fingerprint", "choices", "usage"]}, "example": {"id": "cmpl-uqkvlQyYK7bGYrRHQ0eXlWi7", "object": "text_completion", "created": 1589478378, "model": "gpt-3.5-turbo-instruct", "system_fingerprint": "fp_44709d6fcb", "choices": [{"text": "\n\nThis is indeed a test", "index": 0, "logprobs": null, "finish_reason": "length"}], "usage": {"prompt_tokens": 5, "completion_tokens": 7, "total_tokens": 12}}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/v1/embeddings": {"post": {"summary": "创建嵌入", "deprecated": false, "description": "获取给定输入的矢量表示，机器学习模型和算法可以轻松使用该表示。\n\n相关指南：[嵌入](https://platform.openai.com/docs/guides/embeddings)\n\n创建表示输入文本的嵌入向量。\n\n", "tags": ["嵌入（Embeddings）"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer {{YOUR_API_KEY}}", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string", "description": "要使用的模型的 ID。您可以使用[List models](https://platform.openai.com/docs/api-reference/models/list) API 来查看所有可用模型，或查看我们的[模型概述](https://platform.openai.com/docs/models/overview)以了解它们的描述。"}, "input": {"type": "string", "description": "输入文本以获取嵌入，编码为字符串或标记数组。要在单个请求中获取多个输入的嵌入，请传递一个字符串数组或令牌数组数组。每个输入的长度不得超过 8192 个标记。"}}, "required": ["model", "input"]}, "example": {"model": "text-embedding-ada-002", "input": "The food was delicious and the waiter..."}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"object": {"type": "string"}, "embedding": {"type": "array", "items": {"type": "number"}}, "index": {"type": "integer"}}}}, "model": {"type": "string"}, "usage": {"type": "object", "properties": {"prompt_tokens": {"type": "integer"}, "total_tokens": {"type": "integer"}}, "required": ["prompt_tokens", "total_tokens"]}}, "required": ["object", "data", "model", "usage"]}, "example": "{\n  \"object\": \"list\",\n  \"data\": [\n    {\n      \"object\": \"embedding\",\n      \"embedding\": [\n        0.0023064255,\n        -0.009327292,\n        .... (1536 floats total for ada-002)\n        -0.0028842222\n      ],\n      \"index\": 0\n    }\n  ],\n  \"model\": \"text-embedding-ada-002\",\n  \"usage\": {\n    \"prompt_tokens\": 8,\n    \"total_tokens\": 8\n  }\n}"}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/v1/models": {"get": {"summary": "列出模型", "deprecated": false, "description": "列出当前可用的型号，并提供每个型号的基本信息，例如所有者和可用性。\n\n", "tags": ["模型（Models）"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer {{YOUR_API_KEY}}", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string"}, "created": {"type": "integer"}, "owned_by": {"type": "string"}}, "required": ["id", "object", "created", "owned_by"]}}}, "required": ["object", "data"]}, "example": "{\n  \"object\": \"list\",\n  \"data\": [\n    {\n      \"id\": \"model-id-0\",\n      \"object\": \"model\",\n      \"created\": 1686935002,\n      \"owned_by\": \"organization-owner\"\n    },\n    {\n      \"id\": \"model-id-1\",\n      \"object\": \"model\",\n      \"created\": 1686935002,\n      \"owned_by\": \"organization-owner\",\n    },\n    {\n      \"id\": \"model-id-2\",\n      \"object\": \"model\",\n      \"created\": 1686935002,\n      \"owned_by\": \"openai\"\n    },\n  ],\n  \"object\": \"list\"\n}"}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/v1/models/{modelid}": {"get": {"summary": "检索模型", "deprecated": false, "description": "检索模型实例，提供有关模型的基本信息，例如所有者和权限。\n\n", "tags": ["模型（Models）"], "parameters": [{"name": "modelid", "in": "path", "description": "用于此请求的模型的 ID", "required": true, "example": "babbage", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer {{YOUR_API_KEY}}", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string"}, "created": {"type": "integer"}, "owned_by": {"type": "string"}}, "required": ["id", "object", "created", "owned_by"]}, "example": {"id": "gpt-3.5-turbo-instruct", "object": "model", "created": 1686935002, "owned_by": "openai"}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/v1/models/{model}": {"get": {"summary": "删除微调模型", "deprecated": false, "description": "删除微调模型。您必须在组织中具有所有者角色才能删除模型。\n\n", "tags": ["模型（Models）"], "parameters": [{"name": "model", "in": "path", "description": "要删除的模型\n\n", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string"}, "deleted": {"type": "boolean"}}, "required": ["id", "object", "deleted"]}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/v1/moderations": {"post": {"summary": "创建内容审核", "deprecated": false, "description": "对文本是否违反 OpenAI 的内容政策进行分类\n\n", "tags": ["审查（Moderations）"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": false, "example": "Bearer {{YOUR_API_KEY}}", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"input": {"type": "string", "description": "要分类的输入文本"}, "model": {"type": "string", "description": "有两种内容审核模型可用：`text-moderation-stable`和`text-moderation-latest`。  默认值`text-moderation-latest`将随着时间的推移自动升级。这可确保您始终使用我们最准确的模型。如果您使用`text-moderation-stable`，我们将在更新模型之前提供提前通知。的准确度`text-moderation-stable`可能略低于 的准确度`text-moderation-latest`。"}}, "required": ["input", "model"]}, "example": {"input": "I want to kill them."}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string", "title": "", "description": "审核任务的唯一标识符"}, "model": {"type": "string", "title": "", "description": "用于审核的模型"}, "results": {"type": "array", "items": {"type": "object", "properties": {"categories": {"type": "object", "properties": {"hate": {"type": "boolean", "title": "", "description": "审核模型检测到是否存在仇恨言论"}, "hate/threatening": {"type": "boolean"}, "self-harm": {"type": "boolean", "title": "", "description": "审核模型检测到是否存在自残内容"}, "sexual": {"type": "boolean", "title": "", "description": "审核模型检测到是否存在性内容"}, "sexual/minors": {"type": "boolean"}, "violence": {"type": "boolean", "title": "", "description": "审核模型检测到是否存在暴力内容"}, "violence/graphic": {"type": "boolean"}}, "required": ["hate", "hate/threatening", "self-harm", "sexual", "sexual/minors", "violence", "violence/graphic"], "title": "", "description": "审核模型检测到的所有分类标签"}, "category_scores": {"type": "object", "properties": {"hate": {"type": "number", "title": "", "description": "审核模型对仇恨言论的得分"}, "hate/threatening": {"type": "number"}, "self-harm": {"type": "number", "title": "", "description": "审核模型对自残内容的得分"}, "sexual": {"type": "number", "title": "", "description": "审核模型对性内容的得分"}, "sexual/minors": {"type": "number"}, "violence": {"type": "number", "title": "", "description": "审核模型对暴力内容的得分"}, "violence/graphic": {"type": "number"}}, "required": ["hate", "hate/threatening", "self-harm", "sexual", "sexual/minors", "violence", "violence/graphic"], "title": "", "description": "审核模型对所有分类标签的得分"}, "flagged": {"type": "boolean", "title": "", "description": "审核模型是否将输入内容标记为可疑内容"}}}, "title": "", "description": "审核结果的详细信息"}}, "required": ["id", "model", "results"]}, "example": {"id": "modr-5MWoLO", "model": "text-moderation-001", "results": [{"categories": {"hate": false, "hate/threatening": true, "self-harm": false, "sexual": false, "sexual/minors": false, "violence": true, "violence/graphic": false}, "category_scores": {"hate": 0.22714105248451233, "hate/threatening": 0.4132447838783264, "self-harm": 0.005232391878962517, "sexual": 0.01407341007143259, "sexual/minors": 0.0038522258400917053, "violence": 0.9223177433013916, "violence/graphic": 0.036865197122097015}, "flagged": true}]}}}, "headers": {}}}, "security": [{"bearer": []}]}}}}