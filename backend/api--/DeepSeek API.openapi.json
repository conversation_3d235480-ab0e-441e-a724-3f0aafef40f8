{"openapi": "3.0.1", "info": {"title": "DeepSeek API", "description": "", "version": "1.0.0"}, "tags": [{"name": "API 文档"}], "paths": {"/chat/completions": {"post": {"summary": "对话补全", "deprecated": false, "description": "根据输入的上下文，来让模型补全对话内容。", "tags": ["API 文档"], "parameters": [{"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "required": true, "example": "Bearer {{API_KEY}}", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"messages": {"type": "array", "items": {"oneOf": [{"type": "object", "properties": {"content": {"type": "string", "description": "system 消息的内容"}, "role": {"type": "string", "description": "该消息的发起角色，其值为 `system`"}, "name": {"type": "string", "description": "可以选填的参与者的名称，为模型提供信息以区分相同角色的参与者"}}, "required": ["content", "role"], "title": "System message"}, {"type": "object", "properties": {"content": {"type": "string", "description": "user 消息的内容"}, "role": {"type": "string", "description": "该消息的发起角色，其值为 `user`"}, "name": {"type": "string", "description": "可以选填的参与者的名称，为模型提供信息以区分相同角色的参与者"}}, "required": ["content", "role"], "title": "User message"}, {"type": "object", "properties": {"content": {"type": "string", "description": "assistant 消息的内容"}, "role": {"type": "string", "description": "该消息的发起角色，其值为 `assistant`"}, "name": {"type": "string", "description": "可以选填的参与者的名称，为模型提供信息以区分相同角色的参与者。"}}, "title": "Assistant message", "required": ["content", "role"]}, {"type": "object", "properties": {"content": {"type": "string", "description": "tool 消息的内容"}, "role": {"type": "string", "description": "该消息的发起角色，其值为 `tool`"}, "tool_call_id": {"type": "string", "description": "此消息所响应的 tool call 的 ID"}}, "required": ["role", "content", "tool_call_id"], "title": "Tool message"}]}, "minItems": 1, "description": "对话的消息列表"}, "model": {"type": "string", "description": "使用的 AI 模型", "enum": ["deepseek-chat", "deepseek-reasoner"]}, "frequency_penalty": {"type": "number", "nullable": true}, "max_tokens": {"type": "integer", "nullable": true}, "presence_penalty": {"type": "number", "nullable": true}, "response_format": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}}, "nullable": true}, "stop": {"oneOf": [{"type": "string", "nullable": true}, {"type": "array", "items": {"type": "string"}, "nullable": true}]}, "stream": {"type": "boolean", "description": "是否启用流式传输", "nullable": true}, "stream_options": {"type": "object", "properties": {"include_usage": {"type": "boolean"}}, "nullable": true}, "temperature": {"type": "number", "nullable": true}, "top_p": {"type": "number", "nullable": true}, "tools": {"type": "null"}, "tool_choice": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {}}]}, "logprobs": {"type": "boolean", "nullable": true}, "top_logprobs": {"type": "integer", "nullable": true}}, "required": ["messages", "model"]}, "example": {"messages": [{"content": "You are a helpful assistant", "role": "system"}, {"content": "你是谁？", "role": "user"}], "model": "deepseek-chat", "frequency_penalty": 0, "max_tokens": 2048, "presence_penalty": 0, "response_format": {"type": "text"}, "stop": null, "stream": false, "stream_options": null, "temperature": 1, "top_p": 1, "tools": null, "tool_choice": "none", "logprobs": false, "top_logprobs": null}}}}, "responses": {"200": {"description": "OK, 返回一个 `chat completion` 对象。", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "choices": {"type": "array", "items": {"type": "object", "properties": {"finish_reason": {"type": "string"}, "index": {"type": "integer"}, "message": {"type": "object", "properties": {"content": {"type": "string", "nullable": true}, "reasoning_content": {"type": "string", "nullable": true}, "tool_calls": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "type": {"type": "string"}, "function": {"type": "object", "properties": {"name": {"type": "string"}, "arguments": {"type": "string"}}, "required": ["name", "arguments"]}}}}, "role": {"type": "string"}}, "required": ["content", "role"]}, "logprobs": {"type": "object", "properties": {"content": {"type": "array", "items": {"type": "object", "properties": {"token": {"type": "string"}, "logprob": {"type": "number"}, "bytes": {"type": "array", "items": {"type": "integer"}, "nullable": true}, "top_logprobs": {"type": "array", "items": {"type": "object", "properties": {"token": {"type": "string"}, "logprob": {"type": "integer"}, "bytes": {"type": "array", "items": {"type": "integer"}}}}}}, "required": ["token", "logprob", "bytes", "top_logprobs"]}, "nullable": true}}, "required": ["content"], "nullable": true}}, "required": ["logprobs"]}}, "created": {"type": "integer"}, "model": {"type": "string"}, "system_fingerprint": {"type": "string"}, "object": {"type": "string"}, "usage": {"type": "object", "properties": {"completion_tokens": {"type": "integer"}, "prompt_tokens": {"type": "integer"}, "prompt_cache_hit_tokens": {"type": "integer"}, "prompt_cache_miss_tokens": {"type": "integer"}, "total_tokens": {"type": "integer"}, "completion_tokens_details": {"type": "object", "properties": {"reasoning_tokens": {"type": "integer"}}, "required": ["reasoning_tokens"]}}, "required": ["completion_tokens", "prompt_tokens", "prompt_cache_hit_tokens", "prompt_cache_miss_tokens", "total_tokens"]}}, "required": ["id", "choices", "created", "model", "system_fingerprint", "object"]}, "example": {"id": "e137bb42-7580-4cb8-88ba-825209cf966b", "choices": [{"index": 0, "message": {"role": "assistant", "content": "Hello! How can I assist you today? 😊"}, "logprobs": null, "finish_reason": "stop"}], "created": 1739112811, "model": "deepseek-chat", "system_fingerprint": "fp_3a5790e1b4", "object": "chat.completion", "usage": {"prompt_tokens": 9, "completion_tokens": 11, "total_tokens": 20, "prompt_tokens_details": {"cached_tokens": 0}, "prompt_cache_hit_tokens": 0, "prompt_cache_miss_tokens": 9}}}}, "headers": {}}, "x-200:Streaming": {"description": "OK, 返回包含一系列 `chat completion chunk` 对象的流式输出。", "content": {"text/event-stream": {"schema": {"type": "null"}, "example": "data: {\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"choices\": [\r\n        {\r\n            \"index\": 0,\r\n            \"delta\": {\r\n                \"content\": \"\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"model\": \"deepseek-chat\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"usage\": null\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \"Hello\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \"!\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \" How\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \" can\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \" I\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \" assist\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \" you\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \" today\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \"?\",\r\n                \"role\": \"assistant\"\r\n            },\r\n            \"finish_reason\": null,\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\"\r\n}\r\n\r\ndata: {\r\n    \"choices\": [\r\n        {\r\n            \"delta\": {\r\n                \"content\": \"\",\r\n                \"role\": null\r\n            },\r\n            \"finish_reason\": \"stop\",\r\n            \"index\": 0,\r\n            \"logprobs\": null\r\n        }\r\n    ],\r\n    \"created\": 1718345013,\r\n    \"id\": \"1f633d8bfc032625086f14113c411638\",\r\n    \"model\": \"deepseek-chat\",\r\n    \"object\": \"chat.completion.chunk\",\r\n    \"system_fingerprint\": \"fp_a49d71b8a1\",\r\n    \"usage\": {\r\n        \"completion_tokens\": 9,\r\n        \"prompt_tokens\": 17,\r\n        \"total_tokens\": 26\r\n    }\r\n}\r\n\r\ndata: [DONE\r\n]"}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/beta/completions": {"post": {"summary": "FIM 补全（Beta）", "deprecated": false, "description": "FIM（Fill-In-the-Middle）补全 API。\n\n用户需要设置 base_url=\"https://api.deepseek.com/beta\" 来使用此功能。", "tags": ["API 文档"], "parameters": [{"name": "Content-Type", "in": "header", "description": "", "required": true, "example": "application/json", "schema": {"type": "string"}}, {"name": "Authorization", "in": "header", "description": "", "required": true, "example": "Bearer {{API_KEY}}", "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "properties": {"model": {"type": "string"}, "prompt": {"type": "string"}, "echo": {"type": "boolean", "nullable": true}, "frequency_penalty": {"type": "number", "nullable": true}, "logprobs": {"type": "integer", "nullable": true}, "max_tokens": {"type": "integer", "nullable": true}, "presence_penalty": {"type": "number", "nullable": true}, "stop": {"type": "null"}, "stream": {"type": "boolean", "nullable": true}, "stream_options": {"type": "null"}, "suffix": {"type": "null"}, "temperature": {"type": "integer"}, "top_p": {"type": "integer"}}, "required": ["model", "prompt"]}, "example": {"model": "deepseek-chat", "prompt": "Once upon a time, ", "echo": false, "frequency_penalty": 0, "logprobs": 0, "max_tokens": 1024, "presence_penalty": 0, "stop": null, "stream": false, "stream_options": null, "suffix": null, "temperature": 1, "top_p": 1}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"id": {"type": "string"}, "choices": {"type": "array", "items": {"type": "object", "properties": {"finish_reason": {"type": "string"}, "index": {"type": "integer"}, "logprobs": {"type": "object", "properties": {"text_offset": {"type": "array", "items": {"type": "integer"}, "nullable": true}, "token_logprobs": {"type": "array", "items": {"type": "number"}, "nullable": true}, "tokens": {"type": "array", "items": {"type": "string"}, "nullable": true}, "top_logprobs": {"type": "array", "items": {"type": "object", "properties": {}}, "nullable": true}}, "nullable": true}, "text": {"type": "string"}}}}, "created": {"type": "integer"}, "model": {"type": "string"}, "system_fingerprint": {"type": "string"}, "object": {"type": "string"}, "usage": {"type": "object", "properties": {"completion_tokens": {"type": "integer"}, "prompt_tokens": {"type": "integer"}, "prompt_cache_hit_tokens": {"type": "integer"}, "prompt_cache_miss_tokens": {"type": "integer"}, "total_tokens": {"type": "integer"}, "completion_tokens_details": {"type": "object", "properties": {"reasoning_tokens": {"type": "integer"}}}}, "required": ["completion_tokens", "prompt_tokens", "prompt_cache_hit_tokens", "prompt_cache_miss_tokens", "total_tokens"]}}, "required": ["id", "choices", "created", "model", "object"]}, "example": {"id": "string", "choices": [{"finish_reason": "stop", "index": 0, "logprobs": {"text_offset": [0], "token_logprobs": [0], "tokens": ["string"], "top_logprobs": [{}]}, "text": "string"}], "created": 0, "model": "string", "system_fingerprint": "string", "object": "text_completion", "usage": {"completion_tokens": 0, "prompt_tokens": 0, "prompt_cache_hit_tokens": 0, "prompt_cache_miss_tokens": 0, "total_tokens": 0, "completion_tokens_details": {"reasoning_tokens": 0}}}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/models": {"get": {"summary": "列出模型", "deprecated": false, "description": "列出可用的模型列表，并提供相关模型的基本信息。请前往[模型 & 价格](https://api-docs.deepseek.com/zh-cn/quick_start/pricing)查看当前支持的模型列表", "tags": ["API 文档"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": true, "example": "Bearer {{API_KEY}}", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK, 返回模型列表", "content": {"application/json": {"schema": {"type": "object", "properties": {"object": {"type": "string"}, "data": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "object": {"type": "string"}, "owned_by": {"type": "string"}}, "required": ["id", "object", "owned_by"]}}}, "required": ["object", "data"]}, "example": {"object": "list", "data": [{"id": "deepseek-chat", "object": "model", "owned_by": "deepseek"}, {"id": "deepseek-reasoner", "object": "model", "owned_by": "deepseek"}]}}}, "headers": {}}}, "security": [{"bearer": []}]}}, "/user/balance": {"get": {"summary": "查询余额", "deprecated": false, "description": "", "tags": ["API 文档"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "required": true, "example": "Bearer {{API_KEY}}", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object", "properties": {"is_available": {"type": "boolean"}, "balance_infos": {"type": "array", "items": {"type": "object", "properties": {"currency": {"type": "string"}, "total_balance": {"type": "string"}, "granted_balance": {"type": "string"}, "topped_up_balance": {"type": "string"}}}}}, "required": ["is_available", "balance_infos"]}, "example": {"is_available": true, "balance_infos": [{"currency": "CNY", "total_balance": "110.00", "granted_balance": "10.00", "topped_up_balance": "100.00"}]}}}, "headers": {}}}, "security": [{"bearer": []}]}}}, "components": {"schemas": {}, "securitySchemes": {"bearer": {"type": "http", "scheme": "bearer"}}}, "servers": [{"url": "https://api.deepseek.com", "description": "正式环境"}], "security": []}