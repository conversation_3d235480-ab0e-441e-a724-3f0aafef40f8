import {
  CreateApiKeyRequest,
  CreateApiKeyResponse,
  UpdateApiKeyRequest,
  GetApiKeysRequest,
  GetApiKeysResponse,
  GetApiCategoriesRequest,
  GetApiCategoriesResponse,
  GetApiKeyDetailResponse,
  GetApiKeyUsageRequest,
  GetApiKeyUsageResponse,
  GetChannelUsageRequest,
  GetChannelUsageResponse
} from '@saito/models';

export interface ApiKeyServiceInterface {
  createApiKey(request: CreateApiKeyRequest, userId: string): Promise<CreateApiKeyResponse>;
  updateApiKey(keyId: string, request: UpdateApiKeyRequest, userId: string): Promise<void>;
  deleteApiKey(keyId: string, userId: string): Promise<void>;
  getApiKeys(request: GetApiKeysRequest, userId: string): Promise<GetApiKeysResponse>;
  getApiCategories(request: GetApiCategoriesRequest): Promise<GetApiCategoriesResponse>;
  getApiKeyDetail(keyId: string, userId: string): Promise<GetApiKeyDetailResponse>;
  getApiKeyUsage(request: GetApiKeyUsageRequest, userId: string): Promise<GetApiKeyUsageResponse>;
  getChannelUsage(request: GetChannelUsageRequest, userId: string): Promise<GetChannelUsageResponse>;
}
