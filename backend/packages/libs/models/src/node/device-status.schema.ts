import { z } from 'zod';

// 定义设备状态枚举
export const DeviceStatusSchema = z.enum([
  'waiting',
  'in-progress',
  'connected',
  'disconnected',
  'failed'
]);

export type DeviceStatus = z.infer<typeof DeviceStatusSchema>;

export const DeviceRegisterRequestSchema = z.object({
  code: z.string(),
  gateway_address: z.string(),
  reward_address: z.string(),
  device_type: z.string().optional(),
  gpu_type: z.string().optional(),
  ip: z.string().optional(),
  local_models: z.any().optional() // 允许传入本地模型信息
});

export type DeviceRegisterRequest = z.infer<typeof DeviceRegisterRequestSchema>;

// Base model format
export const OllamaModel = z.object({
  name: z.string(),
  modified_at: z.string(),
  size: z.number(),
  digest: z.string(),
  details: z.object({
    format: z.string(),
    family: z.string(),
    families: z.array(z.string()).nullable(),
    parameter_size: z.string(),
    quantization_level: z.string(),
  }),
});

// List models response
export const OllamaModelList = z.object({
  models: z.array(OllamaModel),
});

export const DeviceRegisterResponseSchema = z.object({
  node_id: z.string(),
  status: DeviceStatusSchema,
  device_type: z.string().optional(),
  reward_address: z.string().optional(),
  local_models: OllamaModelList.optional()
});

export type DeviceRegisterResponse = z.infer<typeof DeviceRegisterResponseSchema>;


export const NewDeviceHeartbeatRequestSchema = z.object({
  code: z.string(),
  cpu_usage: z.number().optional(),
  memory_usage: z.number().optional(),
  gpu_usage: z.number().optional(),
  ip: z.string().optional(),
  timestamp: z.string().optional(),
  type: z.string().optional(),
  model: z.string().optional(),
  device_info: z.object({
    cpu_model: z.string().optional(),
    cpu_cores: z.number().optional(),
    cpu_threads: z.number().optional(),
    ram_total: z.number().optional(),
    gpu_model: z.string().optional(),
    gpu_count: z.number().optional(),
    gpu_memory: z.number().optional(),
    disk_total: z.number().optional(),
    os_info: z.string().optional()
  }).optional(),
  gateway_url: z.string().optional(),
  device_id: z.string().optional()
});

export type NewDeviceHeartbeatRequest = z.infer<typeof NewDeviceHeartbeatRequestSchema>;

/**
 * 用于创建设备时返回的ID
 */
export const DeviceIdResultSchema = z.object({
  id: z.string().uuid().describe('Device ID')
});

export type DeviceIdResult = z.infer<typeof DeviceIdResultSchema>;

/**
 * 用于创建连接任务时返回的结果
 */
export const ConnectTaskResultSchema = z.object({
  id: z.string().uuid().describe('Connect Task ID'),
  one_time_code: z.string().describe('One-time code for device connection')
});

export type ConnectTaskResult = z.infer<typeof ConnectTaskResultSchema>;

/**
 * 用于通过one-time code获取连接任务的结果
 */
export const ConnectTaskByCodeResultSchema = z.object({
  id: z.string().uuid().describe('Connect Task ID'),
  task_name: z.string().describe('Task name'),
  user_id: z.string().uuid().describe('User ID'),
  owner_address: z.string().describe('Owner address'),
  reward_address: z.string().optional().nullable().describe('Reward address'),
  signature: z.string().describe('Signature'),
  node_id: z.string().uuid().describe('Node ID'),
  one_time_code: z.string().describe('One-time code'),
  gateway_address: z.string().optional().nullable().describe('Gateway address'),
  device_type: z.string().optional().nullable().describe('Device type'),
  gpu_type: z.string().optional().nullable().describe('GPU type'),
  status: z.string().describe('Status'),
  created_at: z.string().describe('Created at'),
  updated_at: z.string().describe('Updated at'),
  device_id: z.string().uuid().describe('Device ID')
});

export type ConnectTaskByCodeResult = z.infer<typeof ConnectTaskByCodeResultSchema>;

/**
 * 用于获取设备状态的结果
 */
export const DeviceStatusResultSchema = z.object({
  status: z.string().describe('Device status')
});

export type DeviceStatusResult = z.infer<typeof DeviceStatusResultSchema>;

/**
 * 用于获取所有设备的结果
 */
export const AllDevicesResultSchema = z.object({
  id: z.string().uuid().describe('Device ID'),
  status: z.string().describe('Device status'),
  last_ping: z.union([z.string(), z.number()]).optional().nullable().describe('Last ping timestamp')
});

export type AllDevicesResult = z.infer<typeof AllDevicesResultSchema>;

/**
 * 用于查找空闲节点的结果
 */
export const IdleNodesResultSchema = z.object({
  node_id: z.string().uuid().describe('Node ID'),
  id: z.string().uuid().describe('Device ID'),
  last_ping: z.union([z.string(), z.number()]).optional().nullable().describe('Last ping timestamp')
});

export type IdleNodesResult = z.infer<typeof IdleNodesResultSchema>;

/**
 * 用于获取设备状态历史的结果
 */
export const DeviceStatusHistoryResultSchema = z.object({
  id: z.string().uuid().describe('Status change ID'),
  device_id: z.string().uuid().describe('Device ID'),
  from_status: z.string().describe('From status'),
  to_status: z.string().describe('To status'),
  change_time: z.union([z.string(), z.number()]).describe('Change time'),
  date: z.string().describe('Date'),
  created_at: z.union([z.string(), z.number()]).describe('Created at')
});

export type DeviceStatusHistoryResult = z.infer<typeof DeviceStatusHistoryResultSchema>;

/**
 * 用于获取设备任务的结果
 */
export const DeviceTasksResultSchema = z.object({
  id: z.string().uuid().describe('Task ID'),
  device_id: z.string().uuid().describe('Device ID'),
  model: z.string().describe('Model'),
  created_at: z.string().describe('Created at'),
  status: z.string().describe('Status'),
  total_duration: z.number().optional().nullable().describe('Total duration'),
  load_duration: z.number().optional().nullable().describe('Load duration'),
  prompt_eval_count: z.number().optional().nullable().describe('Prompt eval count'),
  prompt_eval_duration: z.number().optional().nullable().describe('Prompt eval duration'),
  eval_count: z.number().optional().nullable().describe('Eval count'),
  eval_duration: z.number().optional().nullable().describe('Eval duration'),
  updated_at: z.string().describe('Updated at')
});

export type DeviceTasksResult = z.infer<typeof DeviceTasksResultSchema>;

/**
 * 用于注册设备的结果
 */
export const RegisterDeviceResultSchema = z.object({
  node_id: z.string().uuid().describe('Node ID'),
  status: z.string().describe('Status'),
  device_type: z.string().optional().nullable().describe('Device type'),
  reward_address: z.string().describe('Reward address')
});

export type RegisterDeviceResult = z.infer<typeof RegisterDeviceResultSchema>;

/**
 * 用于通过code获取设备的结果
 */
export const DeviceByCodeResultSchema = z.object({
  id: z.string().uuid().describe('Device ID'),
  name: z.string().optional().nullable().describe('Device name'),
  user_id: z.string().uuid().describe('User ID'),
  owner_address: z.string().optional().nullable().describe('Owner address'),
  reward_address: z.string().optional().nullable().describe('Reward address'),
  status: z.string().describe('Status'),
  device_type: z.string().optional().nullable().describe('Device type'),
  created_at: z.string().describe('Created at'),
  updated_at: z.string().describe('Updated at')
});

export type DeviceByCodeResult = z.infer<typeof DeviceByCodeResultSchema>;

/**
 * 用于获取设备的简单结果
 */
export const SimpleDeviceResultSchema = z.object({
  id: z.string().uuid().describe('Device ID'),
  status: z.string().describe('Status'),
  name: z.string().optional().nullable().describe('Device name'),
  device_type: z.string().optional().nullable().describe('Device type'),
  gpu_model: z.string().optional().nullable().describe('GPU model'),
  last_ping: z.union([z.string(), z.number()]).optional().nullable().describe('Last ping')
});

export type SimpleDeviceResult = z.infer<typeof SimpleDeviceResultSchema>;

/**
 * 用于获取连接任务的结果
 */
export const ConnectTaskByIdResultSchema = z.object({
  task_id: z.string().uuid().describe('Task ID'),
  status: z.string().describe('Status'),
  device_id: z.string().uuid().describe('Device ID'),
  created_at: z.string().describe('Created at'),
  updated_at: z.string().describe('Updated at')
});

export type ConnectTaskByIdResult = z.infer<typeof ConnectTaskByIdResultSchema>;

/**
 * 用于获取设备模型的结果
 */
export const DeviceModelSchema = z.object({
  id: z.string().uuid().describe('Model ID'),
  device_id: z.string().uuid().describe('Device ID'),
  model_name: z.string().describe('Model name'),
  model_family: z.string().optional().nullable().describe('Model family'),
  parameter_size: z.string().optional().nullable().describe('Parameter size'),
  quantization_level: z.string().optional().nullable().describe('Quantization level'),
  format: z.string().optional().nullable().describe('Format'),
  size_bytes: z.number().optional().nullable().describe('Size in bytes'),
  digest: z.string().optional().nullable().describe('Digest'),
  modified_at: z.string().optional().nullable().describe('Modified at'),
  created_at: z.string().describe('Created at'),
  updated_at: z.string().describe('Updated at')
});

export type DeviceModel = z.infer<typeof DeviceModelSchema>;


// Device schema - 根据数据库中的定义
export const DeviceSchema = z.object({
  id: z.string().uuid(),
  name: z.string().optional(),
  user_id: z.string().uuid(),
  owner_address: z.string().optional(),
  reward_address: z.string().optional(),
  status: DeviceStatusSchema,
  device_type: z.string().optional(),
  cpu_model: z.string().optional(),
  cpu_cores: z.number().optional(),
  cpu_threads: z.number().optional(),
  cpu_usage_percent: z.number().optional(),
  ram_total: z.number().optional(),
  ram_available: z.number().optional(),
  gpu_model: z.string().optional(),
  gpu_count: z.number().optional(),
  gpu_memory: z.number().optional(),
  gpu_temperature: z.number().optional(),
  disk_total: z.number().optional(),
  disk_available: z.number().optional(),
  ip_address: z.string().optional(),
  last_ping: z.string().optional(),
  latency: z.number().optional(),
  uptime_seconds: z.number().optional(),
  last_boot: z.string().optional(),
  os_info: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string(),
  last_error: z.string().optional(),
  firmware_version: z.string().optional(),
  software_version: z.string().optional(),
  last_maintenance_at: z.string().optional(),
  next_maintenance_at: z.string().optional(),
  health_score: z.number().optional(),
  tags: z.array(z.string()).optional(),
  // 计算字段 - 不在数据库中，但在API响应中需要
  total_earnings: z.number().optional(),
  pending_earnings: z.number().optional(),
  current_model: z.string().optional() // 当前运行的模型
});

export type Device = z.infer<typeof DeviceSchema>;

// Connect task schema - 根据数据库中的定义
export const ConnectTaskSchema = z.object({
  id: z.string().uuid(),
  task_name: z.string(),
  user_id: z.string().uuid(),
  owner_address: z.string(),
  reward_address: z.string().optional(),
  signature: z.string(),
  node_id: z.string().uuid(),
  one_time_code: z.string(),
  gateway_address: z.string().optional(),
  device_type: z.string().optional(),
  gpu_type: z.string().optional(),
  created_at: z.string(),
  updated_at: z.string(),
  status: DeviceStatusSchema
});

export type ConnectTask = z.infer<typeof ConnectTaskSchema>;

// Create connect task request
export const CreateConnectTaskRequestSchema = z.object({
  task_name: z.string(),
  signature: z.string(),
  device_type: z.string().optional(),
  gpu_type: z.string().optional()
});

export type CreateConnectTaskRequest = z.infer<typeof CreateConnectTaskRequestSchema>;

// Create connect task response
export const CreateConnectTaskResponseSchema = z.object({
  task_id: z.string().uuid(),
  one_time_code: z.string(),
  gateway_address: z.string()
});

export type CreateConnectTaskResponse = z.infer<typeof CreateConnectTaskResponseSchema>;

// Device heartbeat request
export const DeviceHeartbeatRequestSchema = z.object({
  node_id: z.string().uuid(),
  status: DeviceStatusSchema,
  cpu_usage_percent: z.number().optional(),
  ram_usage_percent: z.number().optional(),
  gpu_usage_percent: z.number().optional(),
  gpu_temperature: z.number().optional(),
  network_in_kbps: z.number().optional(),
  network_out_kbps: z.number().optional(),
  uptime_seconds: z.number().optional(),
  model: z.string().optional() // 当前运行的模型
});

export type DeviceHeartbeatRequest = z.infer<typeof DeviceHeartbeatRequestSchema>;

// Device metrics schema - 根据数据库中的定义
export const DeviceMetricsSchema = z.object({
  id: z.string().uuid(),
  device_id: z.string().uuid(),
  timestamp: z.union([z.string(), z.number()]), // 可以是字符串或数字
  cpu_usage_percent: z.number().optional(),
  ram_usage_percent: z.number().optional(),
  gpu_usage_percent: z.number().optional(),
  gpu_temperature: z.number().optional(),
  network_in_kbps: z.number().optional(),
  network_out_kbps: z.number().optional(),
  date: z.string(),
  created_at: z.string().optional(), // 设为可选
  updated_at: z.string().optional()  // 设为可选
});

export type DeviceMetrics = z.infer<typeof DeviceMetricsSchema>;

// Device list request
export const DeviceListRequestSchema = z.object({
  page: z.number().default(1),
  pageSize: z.number().default(10),
  status: z.string().optional(),
  search: z.string().optional(),
  onlyMyDevices: z.string().optional()
});

export type DeviceListRequest = z.infer<typeof DeviceListRequestSchema>;

// Device list response
export const DeviceListResponseSchema = z.object({
  data: z.array(DeviceSchema),
  total: z.number(),
  page: z.number(),
  pageSize: z.number()
});

export type DeviceListResponse = z.infer<typeof DeviceListResponseSchema>;

// Device connection request
export const DeviceConnectionRequestSchema = z.object({
  one_time_code: z.string(),
  device_info: z.object({
    cpu_model: z.string().optional(),
    cpu_cores: z.number().optional(),
    cpu_threads: z.number().optional(),
    ram_total: z.number().optional(),
    gpu_model: z.string().optional(),
    gpu_count: z.number().optional(),
    gpu_memory: z.number().optional(),
    disk_total: z.number().optional(),
    os_info: z.string().optional(),
    ip_address: z.string().optional()
  })
});

export type DeviceConnectionRequest = z.infer<typeof DeviceConnectionRequestSchema>;

// Device connection response
export const DeviceConnectionResponseSchema = z.object({
  node_id: z.string().uuid(),
  status: DeviceStatusSchema
});

export type DeviceConnectionResponse = z.infer<typeof DeviceConnectionResponseSchema>;

// Device status change schema - 根据数据库中的定义
export const DeviceStatusChangeSchema = z.object({
  id: z.string().uuid(),
  device_id: z.string().uuid(),
  from_status: DeviceStatusSchema,
  to_status: DeviceStatusSchema,
  change_time: z.union([z.string(), z.number()]),
  date: z.string(),
  created_at: z.union([z.string(), z.number()])
});

export type DeviceStatusChange = z.infer<typeof DeviceStatusChangeSchema>;

/**
 * 节点模型
 */
export const ChatNodeSchema = z.object({
  id: z.string().uuid().describe('Node ID'),
  node_id: z.string().uuid().describe('Node ID'),
  metrics: z.object({
    cpu_usage_percent: z.number().describe('CPU usage percentage'),
    ram_usage_percent: z.number().describe('RAM usage percentage'),
    gpu_usage_percent: z.number().describe('GPU usage percentage')
  }).optional().describe('Node metrics'),
  score: z.number().optional().describe('Node score')
});

export type ChatNode = z.infer<typeof ChatNodeSchema>;

/**
 * 设备指标模型
 */
export const ChatDeviceMetricsSchema = z.object({
  id: z.string().uuid().describe('Metrics ID'),
  device_id: z.string().uuid().describe('Device ID'),
  cpu_usage_percent: z.number().describe('CPU usage percentage'),
  ram_usage_percent: z.number().describe('RAM usage percentage'),
  gpu_usage_percent: z.number().describe('GPU usage percentage'),
  gpu_temperature: z.number().optional().describe('GPU temperature'),
  network_in_kbps: z.number().optional().describe('Network in kbps'),
  network_out_kbps: z.number().optional().describe('Network out kbps'),
  timestamp: z.union([z.string(), z.number()]).describe('Timestamp of metrics collection'),
  date: z.string().describe('Date of metrics collection'),
  created_at: z.string().optional().describe('Creation timestamp'),
  updated_at: z.string().optional().describe('Update timestamp')
});

export type ChatDeviceMetrics = z.infer<typeof ChatDeviceMetricsSchema>;

/**
 * 设备模型
 */
export const ChatDeviceSchema = z.object({
  id: z.string().uuid().describe('Device ID'),
  user_id: z.string().uuid().describe('User ID'),
  name: z.string().describe('Device name'),
  device_type: z.string().describe('Device type'),
  node_id: z.string().uuid().describe('Node ID'),
  status: z.string().describe('Device status'),
  gpu_type: z.string().optional().describe('GPU type'),
  cpu_type: z.string().optional().describe('CPU type'),
  ram_size: z.number().optional().describe('RAM size in GB'),
  created_at: z.string().describe('Creation timestamp'),
  updated_at: z.string().describe('Update timestamp')
});

export type ChatDevice = z.infer<typeof ChatDeviceSchema>;


// Export all schemas
export const NodeSchema = {
  Device: DeviceSchema,
  ConnectTask: ConnectTaskSchema,
  CreateConnectTaskRequest: CreateConnectTaskRequestSchema,
  CreateConnectTaskResponse: CreateConnectTaskResponseSchema,
  DeviceHeartbeatRequest: DeviceHeartbeatRequestSchema,
  DeviceMetrics: DeviceMetricsSchema,
  DeviceListRequest: DeviceListRequestSchema,
  DeviceListResponse: DeviceListResponseSchema,
  DeviceConnectionRequest: DeviceConnectionRequestSchema,
  DeviceConnectionResponse: DeviceConnectionResponseSchema,
  DeviceStatusChange: DeviceStatusChangeSchema,
  DeviceRegisterRequest: DeviceRegisterRequestSchema,
  DeviceRegisterResponse: DeviceRegisterResponseSchema,
  NewDeviceHeartbeatRequest: NewDeviceHeartbeatRequestSchema,
  DeviceStatus: DeviceStatusSchema,
  DeviceModel: DeviceModelSchema,
  // Repository schemas
  DeviceIdResult: DeviceIdResultSchema,
  ConnectTaskResult: ConnectTaskResultSchema,
  ConnectTaskByCodeResult: ConnectTaskByCodeResultSchema,
  DeviceStatusResult: DeviceStatusResultSchema,
  AllDevicesResult: AllDevicesResultSchema,
  IdleNodesResult: IdleNodesResultSchema,
  DeviceStatusHistoryResult: DeviceStatusHistoryResultSchema,
  DeviceTasksResult: DeviceTasksResultSchema,
  RegisterDeviceResult: RegisterDeviceResultSchema,
  DeviceByCodeResult: DeviceByCodeResultSchema,
  SimpleDeviceResult: SimpleDeviceResultSchema,
  ConnectTaskByIdResult: ConnectTaskByIdResultSchema,
};
