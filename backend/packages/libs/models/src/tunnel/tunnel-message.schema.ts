import { z } from 'zod';
import {
  OpenAIChatCompletionRequestSchema,
  OpenAIChatCompletionResponseSchema,
  OpenAIChatCompletionChunkSchema,
  OpenAICompletionRequestSchema,
  OpenAICompletionResponseSchema,
  OpenAIModelSchema
} from '../chat/openai.schema';

export const BaseTunnelMessageSchema = z.object({
  from: z.string(),
  to: z.string(),
  type: z.string().describe('Message type'),
  payload: z.any().describe('Message payload')
});

export type BaseTunnelMessage = z.infer<typeof BaseTunnelMessageSchema>;

// Ping - Pong Message Group
export const PingMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('ping'),
  payload: z.object({
    message: z.string().describe('Message payload'),
    timestamp: z.number().describe('Unix timestamp in milliseconds')
  }).describe('Ping message payload'),
});

export type PingMessage = z.infer<typeof PingMessageSchema>;

export const PongMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('pong'),
  payload: z.object({
    message: z.string().describe('Message payload'),
    timestamp: z.number().describe('Timestamp when pong was sent')
  }).describe('Pong message payload'),
});

export type PongMessage = z.infer<typeof PongMessageSchema>;

// Context Ping - Pong Message Group
export const ContextPingMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('context-ping'),
  payload: z.object({
    requestId: z.string().describe('Request ID'),
    message: z.string().describe('Message payload'),
    timestamp: z.number().describe('Unix timestamp in milliseconds')
  }).describe('Context Ping message payload'),
});

export type ContextPingMessage = z.infer<typeof ContextPingMessageSchema>;

export const ContextPongMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('context-pong'),
  payload: z.object({
    requestId: z.string().describe('Request ID'),
    message: z.string().describe('Message payload'),
    timestamp: z.number().describe('Timestamp when pong was sent')
  }).describe('Context Pong message payload'),
});

export type ContextPongMessage = z.infer<typeof ContextPongMessageSchema>;

export const NoStreamChatRequestMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('chat_request_no_stream'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    data: z.object({
      model: z.string(),
      messages: z.array(
        z.object({
          role: z.string(),
          content: z.string(),
        })
      ),
      stream: z.boolean().optional().default(false)
    })
  })
})

export type NoStreamChatRequestMessage = z.infer<typeof NoStreamChatRequestMessageSchema>;

export const NoStreamChatResponseMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('chat_response'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    data: z.object({
      id: z.string(),
      object: z.literal('chat.completion'),
      created: z.number(), // Unix timestamp
      model: z.string(),
      system_fingerprint: z.string(),
      choices: z.array(
        z.object({
          index: z.number(),
          message: z.object({
            role: z.enum(['assistant', 'user', 'system', 'tool']),
            content: z.string(),
          }),
          finish_reason: z.string(),
        })
      ),
      usage: z.object({
        prompt_tokens: z.number(),
        completion_tokens: z.number(),
        total_tokens: z.number(),
      }),
    })
  })
})

export type NoStreamChatResponseMessage = z.infer<typeof NoStreamChatResponseMessageSchema>;

// Extended OpenAI Completion Request with tunnel-specific fields (定义在使用之前)
export const TunnelOpenAICompletionRequest = OpenAICompletionRequestSchema.extend({
  // Custom fields for tunnel messages
  task_id: z.string().optional(),
  device_id: z.string().optional(),
});

// Completion Request Messages (流式和非流式)
export const StreamCompletionRequestMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('completion_request_stream'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    path: z.string().describe('Request path'),
    data: TunnelOpenAICompletionRequest
  })
})

export type StreamCompletionRequestMessage = z.infer<typeof StreamCompletionRequestMessageSchema>;

export const NoStreamCompletionRequestMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('completion_request_no_stream'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    data: z.object({
      model: z.string(),
      prompt: z.string(),
      stream: z.boolean().optional().default(false)
    })
  })
})

export type NoStreamCompletionRequestMessage = z.infer<typeof NoStreamCompletionRequestMessageSchema>;

// Completion Response Messages (流式和非流式)
export const StreamCompletionResponseMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('completion_response_stream'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    data: z.object({
      id: z.string(),
      object: z.literal('text_completion'),
      created: z.number(),
      model: z.string(),
      choices: z.array(
        z.object({
          text: z.string(),
          index: z.number(),
          logprobs: z.any().nullable(),
          finish_reason: z.string().nullable(),
        })
      ),
      usage: z.object({
        prompt_tokens: z.number(),
        completion_tokens: z.number(),
        total_tokens: z.number(),
      }).optional()
    })
  })
})

export type StreamCompletionResponseMessage = z.infer<typeof StreamCompletionResponseMessageSchema>;

export const NoStreamCompletionResponseMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('completion_response'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    data: z.object({
      id: z.string(),
      object: z.literal('text_completion'),
      created: z.number(),
      model: z.string(),
      choices: z.array(
        z.object({
          text: z.string(),
          index: z.number(),
          logprobs: z.any().nullable(),
          finish_reason: z.string(),
        })
      ),
      usage: z.object({
        prompt_tokens: z.number(),
        completion_tokens: z.number(),
        total_tokens: z.number(),
      }),
    })
  })
})

export type NoStreamCompletionResponseMessage = z.infer<typeof NoStreamCompletionResponseMessageSchema>;

// Chat message format for tunnel messages
const ChatMessage = z.object({
  role: z.string(),
  content: z.string(),
});

// Extended OpenAI Chat Completion Request with tunnel-specific fields
export const TunnelOpenAIChatCompletionRequest = OpenAIChatCompletionRequestSchema.extend({
  // Custom fields for tunnel messages
  task_id: z.string().optional(),
  device_id: z.string().optional(),
});



// Extended OpenAI Embeddings Request with tunnel-specific fields
export const TunnelOpenAIEmbeddingsRequest = z.object({
  model: z.string(),
  input: z.union([z.string(), z.array(z.string())]),
  encoding_format: z.enum(['float', 'base64']).optional(),
  // Custom fields for tunnel messages
  task_id: z.string().optional(),
  device_id: z.string().optional(),
});

// Extended OpenAI Embeddings Response
export const TunnelOpenAIEmbeddingsResponse = z.object({
  object: z.literal('list'),
  data: z.array(z.object({
    object: z.literal('embedding'),
    embedding: z.array(z.number()),
    index: z.number(),
  })),
  model: z.string(),
  usage: z.object({
    prompt_tokens: z.number(),
    total_tokens: z.number(),
  }),
});

// OpenAI Models List Response
export const TunnelOpenAIModelsList = z.object({
  object: z.literal('list'),
  data: z.array(OpenAIModelSchema),
});


export const StreamChatRequestMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('chat_request_stream'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    path: z.string().describe('Request path'),
    data: TunnelOpenAIChatCompletionRequest
  })
})

export type StreamChatRequestMessage = z.infer<typeof StreamChatRequestMessageSchema>;

// Stream Chat Response Message
export const StreamChatResponseMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('chat_response_stream'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    data: OpenAIChatCompletionChunkSchema
  })
})

export type StreamChatResponseMessage = z.infer<typeof StreamChatResponseMessageSchema>;

// Chat Error Message
export const ChatErrorMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('chat_error'),
  payload: z.object({
    taskId: z.string().describe('Task ID'),
    error: z.object({
      message: z.string(),
      type: z.string().optional(),
      code: z.string().optional()
    })
  })
})

export type ChatErrorMessage = z.infer<typeof ChatErrorMessageSchema>;

// Registration

export const NodeRegisterMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('node_register'),
  payload: z.object({
    code: z.string(),
    peer_id: z.string(),
    version: z.string(),
    capabilities: z.array(z.string())
  })
})

export type NodeRegisterMessage = z.infer<typeof NodeRegisterMessageSchema>;

export const TestMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('test'),
  payload: z.object({})
})

// Device Registration Message
export const DeviceRegisterMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('device_register'),
  payload: z.object({
    deviceId: z.string().uuid().describe('Device ID'),
    version: z.string().optional().describe('Device version'),
    capabilities: z.array(z.string()).optional().describe('Device capabilities')
  })
})

export type DeviceRegisterMessage = z.infer<typeof DeviceRegisterMessageSchema>;

// Device Registration Acknowledgment Message
export const DeviceRegisterAckMessageSchema = BaseTunnelMessageSchema.extend({
  type: z.literal('device_register_ack'),
  payload: z.object({
    success: z.boolean().describe('Registration success'),
    deviceId: z.string().uuid().describe('Device ID'),
    error: z.string().optional().describe('Error message if registration failed')
  })
})



export type DeviceRegisterAckMessage = z.infer<typeof DeviceRegisterAckMessageSchema>;

export const TunnelMessageSchema = z.discriminatedUnion('type', [
  PingMessageSchema,
  PongMessageSchema,
  NoStreamChatRequestMessageSchema,
  NoStreamChatResponseMessageSchema,
  StreamChatRequestMessageSchema,
  StreamChatResponseMessageSchema,
  StreamCompletionRequestMessageSchema,
  NoStreamCompletionRequestMessageSchema,
  StreamCompletionResponseMessageSchema,
  NoStreamCompletionResponseMessageSchema,
  ChatErrorMessageSchema,
  NodeRegisterMessageSchema,
  TestMessageSchema,
  ContextPingMessageSchema,
  ContextPongMessageSchema,
  DeviceRegisterMessageSchema,
  DeviceRegisterAckMessageSchema
]);

export type TunnelMessage = z.infer<typeof TunnelMessageSchema>;
