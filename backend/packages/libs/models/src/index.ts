export * from './app';
export * from './auth';
export * from './utils';
export * from './node';
export * from './earnings';
export * from './apikey';
export * from './chat';
export * from './task';
export * from './tunnel';
export * from './inference';
export * from './m.model';
export * from './did';


import { AppSchema } from './app';
import { AuthSchema } from './auth';
import { NodeSchema } from './node';
import * as EarningsSchema from './earnings';
import * as ApiKeySchema from './apikey';
import * as ChatSchema from './chat';
import { TaskSchemas } from './task';
import { TunnelSchemas } from './tunnel';

export const Schema = {
  ...AppSchema,
  ...AuthSchema,
  ...NodeSchema,
  Earnings: EarningsSchema,
  ApiKey: ApiKeySchema,
  Chat: ChatSchema,
  Task: TaskSchemas,
  Tunnel: TunnelSchemas
};
