import { z } from 'zod';

/**
 * Task status schema
 */
export const TaskStatusSchema = z.enum(['pending', 'running', 'completed', 'failed', 'cancelled']);
export type TaskStatus = z.infer<typeof TaskStatusSchema>;

/**
 * Task metrics schema
 */
export const TaskMetricsSchema = z.object({
  total_duration: z.number().describe('Total task duration in milliseconds'),
  load_duration: z.number().describe('Model load duration in milliseconds'),
  prompt_eval_count: z.number().describe('Number of prompt evaluations'),
  prompt_eval_duration: z.number().describe('Prompt evaluation duration in milliseconds'),
  eval_count: z.number().describe('Number of evaluations'),
  eval_duration: z.number().describe('Evaluation duration in milliseconds')
});

export type TaskMetrics = z.infer<typeof TaskMetricsSchema>;

/**
 * Task schema - 根据数据库中的定义
 */
export const TaskSchema = z.object({
  id: z.string().uuid().describe('Task ID'),
  device_id: z.string().uuid().describe('Device ID'),
  user_id: z.string().uuid().optional().describe('User ID'),
  model: z.string().describe('AI model used'),
  status: TaskStatusSchema.describe('Task status'),
  total_duration: z.number().optional().describe('Total task duration in milliseconds'),
  load_duration: z.number().optional().describe('Model load duration in milliseconds'),
  prompt_eval_count: z.number().optional().describe('Number of prompt evaluations'),
  prompt_eval_duration: z.number().optional().describe('Prompt evaluation duration in milliseconds'),
  eval_count: z.number().optional().describe('Number of evaluations'),
  eval_duration: z.number().optional().describe('Evaluation duration in milliseconds'),
  created_at: z.string().describe('Task creation timestamp'),
  updated_at: z.string().describe('Task update timestamp')
});

export type Task = z.infer<typeof TaskSchema>;

/**
 * Create task request schema
 */
export const CreateTaskRequestSchema = z.object({
  device_id: z.string().uuid().describe('Device ID'),
  model: z.string().describe('AI model to use'),
  user_id: z.string().uuid().optional().describe('User ID')
});

export type CreateTaskRequest = z.infer<typeof CreateTaskRequestSchema>;

/**
 * Create task response schema
 */
export const CreateTaskResponseSchema = z.object({
  id: z.string().uuid().describe('Created task ID')
});

export type CreateTaskResponse = z.infer<typeof CreateTaskResponseSchema>;

/**
 * Task operation response schema (for start, complete, fail, cancel operations)
 */
export const TaskOperationResponseSchema = z.object({
  id: z.string().uuid().describe('Task ID')
});

export type TaskOperationResponse = z.infer<typeof TaskOperationResponseSchema>;

/**
 * Get task response schema
 */
export const GetTaskResponseSchema = z.object({
  id: z.string().uuid().describe('Task ID'),
  device_id: z.string().uuid().describe('Device ID'),
  user_id: z.string().uuid().optional().describe('User ID'),
  model: z.string().describe('AI model used'),
  status: TaskStatusSchema.describe('Task status'),
  total_duration: z.number().optional().describe('Total task duration in milliseconds'),
  load_duration: z.number().optional().describe('Model load duration in milliseconds'),
  prompt_eval_count: z.number().optional().describe('Number of prompt evaluations'),
  prompt_eval_duration: z.number().optional().describe('Prompt evaluation duration in milliseconds'),
  eval_count: z.number().optional().describe('Number of evaluations'),
  eval_duration: z.number().optional().describe('Evaluation duration in milliseconds'),
  created_at: z.string().describe('Task creation timestamp'),
  updated_at: z.string().describe('Task update timestamp')
});

export type GetTaskResponse = z.infer<typeof GetTaskResponseSchema>;

/**
 * Task list request schema
 */
export const TaskListRequestSchema = z.object({
  page: z.number().default(1).describe('Page number'),
  pageSize: z.number().default(10).describe('Page size'),
  status: z.string().optional().describe('Filter by status'),
  deviceId: z.string().uuid().optional().describe('Filter by device ID'),
  userId: z.string().uuid().optional().describe('Filter by user ID'),
  timeRange: z.string().optional().describe('Time range for data')
});

export type TaskListRequest = z.infer<typeof TaskListRequestSchema>;

/**
 * Task list response schema
 */
export const TaskListResponseSchema = z.object({
  data: z.array(TaskSchema).describe('List of tasks'),
  total: z.number().describe('Total number of tasks'),
  page: z.number().describe('Current page'),
  pageSize: z.number().describe('Page size')
});

export type TaskListResponse = z.infer<typeof TaskListResponseSchema>;

/**
 * Slonik SQL查询专用schema
 */

/**
 * 用于返回任务ID的schema
 */
export const TaskIdResultSchema = z.object({
  id: z.string().uuid().describe('Task ID')
});

export type TaskIdResult = z.infer<typeof TaskIdResultSchema>;

/**
 * 用于数据库行的schema，包含可能为null的字段
 */
export const TaskRowSchema = z.object({
  id: z.string().uuid(),
  device_id: z.string().uuid(),
  model: z.string(),
  status: z.string(),
  created_at: z.string(),
  updated_at: z.string(),
  user_id: z.string().uuid().optional().nullable(),
  total_duration: z.number().optional().nullable(),
  load_duration: z.number().optional().nullable(),
  prompt_eval_count: z.number().optional().nullable(),
  prompt_eval_duration: z.number().optional().nullable(),
  eval_count: z.number().optional().nullable(),
  eval_duration: z.number().optional().nullable()
});
