import { Inject, Logger } from "@nestjs/common";
import { PersistentService } from "@saito/persistent";
import { DatabaseTransactionConnection, sql } from "slonik";
import * as R from 'ramda';
import {
  Device,
  DeviceIdResultSchema,
  ConnectTaskResultSchema,
  ConnectTaskByCodeResultSchema,
  AllDevicesResultSchema,
  IdleNodesResultSchema,
  DeviceStatusHistoryResultSchema,
  DeviceTasksResultSchema,
  DeviceByCodeResultSchema,
  SimpleDeviceResultSchema,
  ConnectTaskByIdResultSchema,
  DeviceModelSchema,
  ChatDeviceMetricsSchema
} from "@saito/models";
import { z } from 'zod';

// 定义设备状态类型
type DeviceStatus = 'waiting' | 'in-progress' | 'connected' | 'disconnected' | 'failed';

export class NodeRepository {
  private readonly logger = new Logger(NodeRepository.name);

  constructor(
    @Inject(PersistentService)
    private readonly persistentService: PersistentService,
  ) { }

  async transaction<T>(handler: (conn: DatabaseTransactionConnection) => Promise<T>) {
    return this.persistentService.pgPool.transaction(handler);
  }

  /**
   * Create a new device
   */
  async createDevice(
    name: string | null,
    userId: string,
    ownerAddress: string | null,
    rewardAddress: string | null,
    deviceType: string | null
  ) {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(DeviceIdResultSchema)`
          INSERT INTO saito_gateway.devices (
            name,
            user_id,
            owner_address,
            reward_address,
            status,
            device_type
          )
          VALUES (
            ${name},
            ${userId},
            ${ownerAddress},
            ${rewardAddress},
            'waiting',
            ${deviceType}
          )
          RETURNING id
        `
      );

      return result.rows[0].id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`创建设备失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Create a new connect task
   */
  async createConnectTask(
    taskName: string,
    userId: string,
    ownerAddress: string,
    rewardAddress: string | null,
    signature: string,
    nodeId: string,
    oneTimeCode: string,
    gatewayAddress: string | null,
    deviceType: string | null,
    gpuType: string | null
  ) {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(ConnectTaskResultSchema)`
          INSERT INTO saito_gateway.connect_tasks (
            task_name,
            user_id,
            owner_address,
            reward_address,
            signature,
            node_id,
            one_time_code,
            gateway_address,
            device_type,
            gpu_type,
            status
          )
          VALUES (
            ${taskName},
            ${userId},
            ${ownerAddress},
            ${rewardAddress},
            ${signature},
            ${nodeId},
            ${oneTimeCode},
            ${gatewayAddress},
            ${deviceType},
            ${gpuType},
            'waiting'
          )
          RETURNING id, one_time_code
        `
      );

      return {
        taskId: result.rows[0].id,
        oneTimeCode: result.rows[0].one_time_code
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`创建连接任务失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get connect task by one-time code
   */
  async getConnectTaskByCode(oneTimeCode: string) {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(ConnectTaskByCodeResultSchema)`
          SELECT
            ct.*,
            d.id as device_id
          FROM
            saito_gateway.connect_tasks ct
          JOIN
            saito_gateway.devices d ON ct.node_id = d.id
          WHERE
            ct.one_time_code = ${oneTimeCode}
        `
      );

      if (R.isEmpty(result.rows)) {
        return null;
      }

      return result.rows[0];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取连接任务失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Update device status
   */
  async updateDeviceStatus(deviceId: string, status: DeviceStatus) {
    try {
      // 使用事务确保数据一致性
      return this.persistentService.pgPool.transaction(async (conn) => {
        // 获取设备当前状态
        const deviceResult = await conn.query(sql.unsafe`
          SELECT status FROM saito_gateway.devices WHERE id = ${deviceId}
        `);

        if (deviceResult.rows.length === 0) {
          throw new Error(`Device not found: ${deviceId}`);
        }

        const currentStatus = deviceResult.rows[0].status;

        // 更新设备状态
        await conn.query(sql.unsafe`
          UPDATE saito_gateway.devices
          SET status = ${status}
          WHERE id = ${deviceId}
        `);

        // 记录状态变更
        await conn.query(sql.unsafe`
          INSERT INTO saito_gateway.device_status_changes (
            device_id,
            from_status,
            to_status,
            change_time,
            date
          )
          VALUES (
            ${deviceId},
            ${currentStatus},
            ${status},
            NOW(),
            CURRENT_DATE
          )
        `);

        // 同步更新连接任务状态
        await conn.query(sql.unsafe`
          UPDATE saito_gateway.connect_tasks
          SET status = ${status}
          WHERE node_id = ${deviceId}
        `);

        this.logger.log(`Updated connect_tasks status to ${status} for device ${deviceId}`);

        // 如果设备状态变为disconnected或failed，更新相关任务状态
        if ((currentStatus === 'connected' && (status === 'disconnected' || status === 'failed'))) {
          await this.updateTasksForDisconnectedDevice(conn, deviceId);
        }

        return { fromStatus: currentStatus, toStatus: status };
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`更新设备状态失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 更新断开连接或失败设备的任务状态
   * 将pending和running状态的任务更新为failed
   * @param conn 数据库连接
   * @param deviceId 设备ID
   */
  private async updateTasksForDisconnectedDevice(
    conn: DatabaseTransactionConnection,
    deviceId: string
  ) {
    try {
      // 查找设备上所有pending或running状态的任务
      const affectedTasksResult = await conn.query(sql.unsafe`
        SELECT id, status FROM saito_gateway.tasks
        WHERE device_id = ${deviceId} AND status IN ('pending', 'running')
      `);

      if (affectedTasksResult.rows.length === 0) {
        return; // 没有需要更新的任务
      }

      // 记录找到的任务数量
      this.logger.log(`Found ${affectedTasksResult.rows.length} pending/running tasks for disconnected device ${deviceId}`);

      // 更新所有pending或running状态的任务为failed
      await conn.query(sql.unsafe`
        UPDATE saito_gateway.tasks
        SET
          status = 'failed',
          updated_at = NOW()
        WHERE
          device_id = ${deviceId}
          AND status IN ('pending', 'running')
      `);

      // 记录每个任务的状态变化
      for (const task of affectedTasksResult.rows) {
        this.logger.log(`Task ${task.id} status changed from ${task.status} to failed`);
      }

      this.logger.log(`Updated ${affectedTasksResult.rows.length} tasks to failed status for device ${deviceId}`);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`更新断开连接设备的任务状态失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Update connect task status
   */
  async updateConnectTaskStatus(taskId: string, status: DeviceStatus) {
    try {
      await this.persistentService.pgPool.query(sql.unsafe`
        UPDATE saito_gateway.connect_tasks
        SET status = ${status}
        WHERE id = ${taskId}
      `);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`更新连接任务状态失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Update device information
   */
  async updateDeviceInfo(
    deviceId: string,
    cpuModel: string | null,
    cpuCores: number | null,
    cpuThreads: number | null,
    ramTotal: number | null,
    gpuModel: string | null,
    gpuCount: number | null,
    gpuMemory: number | null,
    diskTotal: number | null,
    osInfo: string | null
  ) {
    try {
      await this.persistentService.pgPool.query(sql.unsafe`
        UPDATE saito_gateway.devices
        SET
          cpu_model = ${cpuModel},
          cpu_cores = ${cpuCores},
          cpu_threads = ${cpuThreads},
          ram_total = ${ramTotal},
          gpu_model = ${gpuModel},
          gpu_count = ${gpuCount},
          gpu_memory = ${gpuMemory},
          disk_total = ${diskTotal},
          os_info = ${osInfo}
        WHERE id = ${deviceId}
      `);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`更新设备信息失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Update device heartbeat
   */
  async updateDeviceHeartbeat(
    deviceId: string,
    status: DeviceStatus,
    cpuUsagePercent: number | null,
    ramUsagePercent: number | null,
    gpuUsagePercent: number | null,
    gpuTemperature: number | null,
    networkInKbps: number | null,
    networkOutKbps: number | null,
    _model: string | null, // 参数名前加下划线表示未使用，保留参数以兼容现有调用
    uptimeSeconds: number | null = null
  ) {
    try {
      // 使用事务确保数据一致性
      await this.transaction(async (conn) => {
        // 获取设备当前状态
        const deviceResult = await conn.query(sql.unsafe`
          SELECT status FROM saito_gateway.devices WHERE id = ${deviceId}
        `);

        if (deviceResult.rows.length === 0) {
          throw new Error(`Device not found: ${deviceId}`);
        }

        const currentStatus = deviceResult.rows[0].status;

        // 如果状态发生变化，记录状态变更
        if (currentStatus !== status) {
          await conn.query(sql.unsafe`
            INSERT INTO saito_gateway.device_status_changes (
              device_id,
              from_status,
              to_status,
              change_time,
              date
            )
            VALUES (
              ${deviceId},
              ${currentStatus},
              ${status},
              NOW(),
              CURRENT_DATE
            )
          `);

          // 如果设备从disconnected变为connected，记录最近失败的任务（仅用于日志记录，不恢复任务）
          if (currentStatus === 'disconnected' && status === 'connected') {
            await this.checkFailedTasksForReconnectedDevice(conn, deviceId);
            // 注意：我们只记录失败的任务，但不自动恢复它们，需要用户手动重新创建任务
          }
        }

        // 确保 uptime_seconds 是整数
        const safeUptimeSeconds = uptimeSeconds !== null ? Math.floor(Number(uptimeSeconds)) : null;

        // 更新设备状态和最后心跳时间，以及性能指标
        await conn.query(sql.unsafe`
          UPDATE saito_gateway.devices
          SET
            status = ${status},
            last_ping = NOW(),
            cpu_usage_percent = ${cpuUsagePercent},
            gpu_temperature = ${gpuTemperature},
            uptime_seconds = ${safeUptimeSeconds}
          WHERE id = ${deviceId}
        `);

        // 记录设备指标到device_metrics表
        await conn.query(sql.unsafe`
          INSERT INTO saito_gateway.device_metrics (
            device_id,
            cpu_usage_percent,
            ram_usage_percent,
            gpu_usage_percent,
            gpu_temperature,
            network_in_kbps,
            network_out_kbps,
            date
          )
          VALUES (
            ${deviceId},
            ${cpuUsagePercent},
            ${ramUsagePercent},
            ${gpuUsagePercent},
            ${gpuTemperature},
            ${networkInKbps},
            ${networkOutKbps},
            CURRENT_DATE
          )
        `);

        // 注释掉自动创建任务的逻辑，根据用户要求不在设备注册时创建任务记录
        // if (_model) {
        //   // 检查是否有正在运行的任务
        //   const taskResult = await conn.query(sql.unsafe`
        //     SELECT id FROM saito_gateway.tasks
        //     WHERE device_id = ${deviceId} AND status = 'running'
        //   `);

        //   // 如果没有正在运行的任务，创建一个新任务
        //   if (taskResult.rows.length === 0) {
        //     await conn.query(sql.unsafe`
        //       INSERT INTO saito_gateway.tasks (
        //         device_id,
        //         model,
        //         status
        //       )
        //       VALUES (
        //         ${deviceId},
        //         ${_model},
        //         'running'
        //       )
        //     `);
        //   }
        // }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`更新设备心跳失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * 检查重新连接设备的失败任务
   * 仅记录最近失败的任务到日志中，不会自动恢复任务状态
   * 即使设备重新上线，任务状态也不会自动从failed变为running
   * @param conn 数据库连接
   * @param deviceId 设备ID
   */
  private async checkFailedTasksForReconnectedDevice(
    conn: DatabaseTransactionConnection,
    deviceId: string
  ) {
    try {
      // 查找设备上最近30分钟内失败的任务
      const failedTasksResult = await conn.query(sql.unsafe`
        SELECT id, model, created_at
        FROM saito_gateway.tasks
        WHERE
          device_id = ${deviceId}
          AND status = 'failed'
          AND updated_at > NOW() - INTERVAL '30 minutes'
        ORDER BY updated_at DESC
        LIMIT 5
      `);

      if (failedTasksResult.rows.length === 0) {
        return; // 没有最近失败的任务
      }

      // 记录找到的失败任务，但不自动恢复它们
      // 这里只是记录日志，实际应用中可能需要通知用户或提供重试机制
      this.logger.log(`Device ${deviceId} reconnected. Found ${failedTasksResult.rows.length} recently failed tasks.`);

      for (const task of failedTasksResult.rows) {
        this.logger.log(`Failed task: ${task.id}, model: ${task.model}, created at: ${task.created_at}`);
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`检查重新连接设备的失败任务失败: ${errorMessage}`);
      // 不抛出错误，避免影响心跳更新流程
    }
  }

  /**
   * 检查超时的连接任务
   * 将超过30分钟未完成的连接任务标记为失败，并更新相关设备状态
   */
  async checkForTimedOutConnectTasks(): Promise<void> {
    try {
      // 使用事务确保数据一致性
      await this.persistentService.pgPool.transaction(async (conn) => {
        // 查找超时的连接任务（30分钟）
        const connectTasksQueryString = `
          UPDATE saito_gateway.connect_tasks
          SET
            status = 'failed',
            updated_at = NOW()
          WHERE
            status IN ('waiting', 'in-progress')
            AND updated_at < NOW() - INTERVAL '30 minutes'
          RETURNING id, node_id
        `;

        // 执行查询
        const connectTasksResult = await conn.query(sql.unsafe([connectTasksQueryString]));

        if (connectTasksResult.rowCount === 0) {
          return; // 没有超时的连接任务
        }

        const timedOutConnectTasks = connectTasksResult.rows;
        this.logger.warn(`Marked ${timedOutConnectTasks.length} connect tasks as failed due to timeout`);

        // 更新相关设备状态为failed
        for (const task of timedOutConnectTasks) {
          try {
            // 更新设备状态为failed
            await conn.query(sql.unsafe`
              UPDATE saito_gateway.devices
              SET
                status = 'failed',
                updated_at = NOW()
              WHERE
                id = ${task.node_id}
            `);

            // 记录设备状态变更
            await conn.query(sql.unsafe`
              INSERT INTO saito_gateway.device_status_changes (
                device_id,
                from_status,
                to_status,
                change_time,
                date
              )
              VALUES (
                ${task.node_id},
                'waiting',
                'failed',
                NOW(),
                CURRENT_DATE
              )
            `);

            this.logger.warn(`Updated device ${task.node_id} status to failed due to connect task ${task.id} timeout`);
          } catch (deviceError) {
            const errorMessage = deviceError instanceof Error ? deviceError.message : String(deviceError);
            this.logger.error(`Error updating device status for timed out connect task ${task.id}: ${errorMessage}`);
          }
        }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`检查超时连接任务时出错: ${errorMessage}`);
    }
  }

  /**
   * Get all devices (for status checking)
   */
  async getAllDevices() {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(AllDevicesResultSchema)`
          SELECT
            id,
            status,
            last_ping
          FROM
            saito_gateway.devices
        `
      );

      return result.rows;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取所有设备失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get devices for a user or all devices
   */
  async getDevices(userId: string, page: number, pageSize: number, status?: string, search?: string) {
    try {
      // Initialize with a default condition that's always true
      let whereClause = sql.unsafe`WHERE 1=1`;
      this.logger.debug(`getDevices userId: ${userId}`);

      // Only filter by userId if it's provided and not empty
      if (userId && userId.trim() !== '') {
        whereClause = sql.unsafe`${whereClause} AND d.user_id = ${userId}`;
      }

      if (status && status !== 'all') {
        whereClause = sql.unsafe`${whereClause} AND d.status = ${status}`;
      }

      if (search) {
        whereClause = sql.unsafe`${whereClause} AND (
          d.name ILIKE ${`%${search}%`} OR
          d.device_type ILIKE ${`%${search}%`} OR
          d.gpu_model ILIKE ${`%${search}%`}
        )`;
      }

      // Get total count
      const countResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT COUNT(*) as total
        FROM saito_gateway.devices d
        ${whereClause}
      `);

      const total = parseInt(countResult.rows[0].total, 10);

      // Get devices with pagination, joining with the latest running task to get the current model
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        WITH devices_to_fetch AS (
          SELECT id FROM saito_gateway.devices d ${whereClause}
          LIMIT ${pageSize} OFFSET ${(page - 1) * pageSize}
        ),
        latest_metrics AS (
          SELECT
            dm.device_id,
            dm.cpu_usage_percent,
            dm.ram_usage_percent,
            dm.gpu_usage_percent,
            dm.gpu_temperature,
            dm.network_in_kbps,
            dm.network_out_kbps,
            ROW_NUMBER() OVER (PARTITION BY dm.device_id ORDER BY dm.date DESC) as rn
          FROM
            saito_gateway.device_metrics dm
          JOIN
            devices_to_fetch dtf ON dm.device_id = dtf.id
        )
        SELECT
          d.id as node_id,
          d.name as task_name,
          d.device_type,
          d.gpu_model as gpu_type,
          d.status,
          d.last_ping,
          d.uptime_seconds,
          -- Get the model from the latest completed task
          (SELECT t.model
           FROM saito_gateway.tasks t
           WHERE t.device_id = d.id AND t.status = 'completed'
           ORDER BY t.created_at DESC
           LIMIT 1) as current_model,
          COALESCE(SUM(e.total_rewards), 0) as total_earnings,
          COALESCE(SUM(CASE WHEN e.date >= CURRENT_DATE - INTERVAL '7 days' THEN e.total_rewards ELSE 0 END), 0) as pending_earnings,
          -- 添加设备硬件使用情况
          lm.cpu_usage_percent,
          lm.ram_usage_percent,
          lm.gpu_usage_percent,
          lm.gpu_temperature,
          lm.network_in_kbps,
          lm.network_out_kbps
        FROM
          saito_gateway.devices d
        JOIN
          devices_to_fetch dtf ON d.id = dtf.id
        LEFT JOIN
          saito_gateway.earnings e ON d.id = e.device_id
        LEFT JOIN
          latest_metrics lm ON d.id = lm.device_id AND lm.rn = 1
        GROUP BY
          d.id, d.name, d.device_type, d.gpu_model, d.status, d.last_ping, d.uptime_seconds,
          lm.cpu_usage_percent, lm.ram_usage_percent, lm.gpu_usage_percent,
          lm.gpu_temperature, lm.network_in_kbps, lm.network_out_kbps
        ORDER BY
          d.created_at DESC
      `);

      return {
        data: result.rows,
        total,
        page,
        pageSize
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取设备列表失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get connect tasks for a user or all connect tasks
   */
  async getConnectTasks(userId: string, page: number, pageSize: number, status?: string, search?: string) {
    try {
      // Initialize with a default condition that's always true
      let whereClause = sql.unsafe`WHERE 1=1`;

      // Only filter by userId if it's provided and not empty
      if (userId && userId.trim() !== '') {
        whereClause = sql.unsafe`${whereClause} AND ct.user_id = ${userId}`;
      }

      if (status && status !== 'all') {
        whereClause = sql.unsafe`${whereClause} AND ct.status = ${status}`;
      }

      if (search) {
        whereClause = sql.unsafe`${whereClause} AND (
          ct.task_name ILIKE ${`%${search}%`} OR
          ct.device_type ILIKE ${`%${search}%`} OR
          ct.gpu_type ILIKE ${`%${search}%`}
        )`;
      }

      // Get total count
      const countResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT COUNT(*) as total
        FROM saito_gateway.connect_tasks ct
        ${whereClause}
      `);

      const total = parseInt(countResult.rows[0].total, 10);

      // Get connect tasks with pagination
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT
          ct.id as node_id,
          ct.task_name,
          ct.device_type,
          ct.gpu_type,
          ct.status,
          ct.created_at,
          ct.updated_at,
          -- Get the model from the latest completed task
          (SELECT t.model
           FROM saito_gateway.tasks t
           WHERE t.device_id = ct.node_id AND t.status = 'completed'
           ORDER BY t.created_at DESC
           LIMIT 1) as current_model
        FROM
          saito_gateway.connect_tasks ct
        ${whereClause}
        ORDER BY
          ct.created_at DESC
        LIMIT
          ${pageSize}
        OFFSET
          ${(page - 1) * pageSize}
      `);

      return {
        data: result.rows,
        total,
        page,
        pageSize
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取连接任务列表失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get device by ID
   */
  async getDevice(deviceId: string, userId: string) {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        WITH latest_metrics AS (
          SELECT
            device_id,
            cpu_usage_percent,
            ram_usage_percent,
            gpu_usage_percent,
            gpu_temperature,
            network_in_kbps,
            network_out_kbps,
            ROW_NUMBER() OVER (PARTITION BY device_id ORDER BY date DESC) as rn
          FROM
            saito_gateway.device_metrics
          WHERE
            device_id = ${deviceId}
        )
        SELECT
          d.*,
          -- Get the model from the latest completed task
          (SELECT t.model
           FROM saito_gateway.tasks t
           WHERE t.device_id = d.id AND t.status = 'completed'
           ORDER BY t.created_at DESC
           LIMIT 1) as current_model,
          COALESCE(SUM(e.total_rewards), 0) as total_earnings,
          COALESCE(SUM(CASE WHEN e.date >= CURRENT_DATE - INTERVAL '7 days' THEN e.total_rewards ELSE 0 END), 0) as pending_earnings,
          -- 添加设备硬件使用情况
          lm.cpu_usage_percent,
          lm.ram_usage_percent,
          lm.gpu_usage_percent,
          lm.gpu_temperature,
          lm.network_in_kbps,
          lm.network_out_kbps
        FROM
          saito_gateway.devices d
        LEFT JOIN
          saito_gateway.earnings e ON d.id = e.device_id
        LEFT JOIN
          latest_metrics lm ON d.id = lm.device_id AND lm.rn = 1
        WHERE
          d.id = ${deviceId}
          ${userId && userId.trim() !== '' ? sql.unsafe`AND d.user_id = ${userId}` : sql.unsafe``}
        GROUP BY
          d.id, lm.cpu_usage_percent, lm.ram_usage_percent, lm.gpu_usage_percent,
          lm.gpu_temperature, lm.network_in_kbps, lm.network_out_kbps
      `);

      if (R.isEmpty(result.rows)) {
        return null;
      }

      return result.rows[0] as Device;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取设备详情失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Find idle nodes
   * @param model Optional model name to filter nodes by supported models
   */
  async findIdleNodes(model?: string) {
    try {
      // 如果没有指定模型，返回所有连接的设备
      if (!model) {
        const result = await this.persistentService.pgPool.query(
          sql.type(IdleNodesResultSchema)`
            SELECT
              d.id as node_id,
              d.id,
              d.last_ping
            FROM
              saito_gateway.devices d
            WHERE
              d.status = 'connected'
            ORDER BY
              d.last_ping DESC
            LIMIT 5
          `
        );
        return result.rows;
      }

      try {
        // 如果指定了模型，只返回支持该模型的设备
        const result = await this.persistentService.pgPool.query(
          sql.type(IdleNodesResultSchema)`
            SELECT DISTINCT
              d.id as node_id,
              d.id,
              d.last_ping
            FROM
              saito_gateway.devices d
            JOIN
              saito_gateway.device_models dm ON d.id = dm.device_id
            WHERE
              d.status = 'connected'
              AND dm.model_name = ${model}
            ORDER BY
              d.last_ping DESC
            LIMIT 5
          `
        );

        return result.rows;
      } catch (modelError: unknown) {
        const modelErrorMessage = modelError instanceof Error ? modelError.message : String(modelError);

        // 如果是表不存在的错误，回退到不过滤模型的查询
        if (modelErrorMessage.includes('relation "saito_gateway.device_models" does not exist')) {


          // 回退到不过滤模型的查询
          const fallbackResult = await this.persistentService.pgPool.query(
            sql.type(IdleNodesResultSchema)`
              SELECT
                d.id as node_id,
                d.id,
                d.last_ping
              FROM
                saito_gateway.devices d
              WHERE
                d.status = 'connected'
              ORDER BY
                d.last_ping DESC
              LIMIT 5
            `
          );

          return fallbackResult.rows;
        }

        // 其他错误则抛出
        throw modelError;
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`查找空闲节点失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get device status history
   */
  async getDeviceStatusHistory(deviceId: string, userId: string, days: number = 7) {
    try {
      // 确保 days 是数字
      const daysNum = Number(days) || 7;

      // 验证设备是否存在，如果提供了userId则同时验证设备是否属于该用户
      let whereClause = sql.unsafe`WHERE id = ${deviceId}`;

      // Only filter by userId if it's provided and not empty
      if (userId && userId.trim() !== '') {
        whereClause = sql.unsafe`${whereClause} AND user_id = ${userId}`;
      }

      const deviceResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT id FROM saito_gateway.devices
        ${whereClause}
      `);

      if (deviceResult.rows.length === 0) {
        throw new Error(`Device not found: ${deviceId}`);
      }

      // 获取状态历史
      const result = await this.persistentService.pgPool.query(
        sql.type(DeviceStatusHistoryResultSchema)`
          SELECT
            id,
            device_id,
            from_status,
            to_status,
            change_time,
            date,
            created_at
          FROM
            saito_gateway.device_status_changes
          WHERE
            device_id = ${deviceId}
            AND date >= CURRENT_DATE - (${daysNum} || ' days')::INTERVAL
          ORDER BY
            change_time DESC
        `
      );

      return result.rows;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取设备状态历史失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get device tasks
   */
  async getDeviceTasks(deviceId: string, userId: string, page: number = 1, pageSize: number = 10, status?: string) {
    try {
      // 验证设备是否存在，如果提供了userId则同时验证设备是否属于该用户
      let deviceWhereClause = sql.unsafe`WHERE id = ${deviceId}`;

      // Only filter by userId if it's provided and not empty
      if (userId && userId.trim() !== '') {
        deviceWhereClause = sql.unsafe`${deviceWhereClause} AND user_id = ${userId}`;
      }

      const deviceResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT id FROM saito_gateway.devices
        ${deviceWhereClause}
      `);

      if (deviceResult.rows.length === 0) {
        throw new Error(`Device not found: ${deviceId}`);
      }

      // 构建查询条件
      let whereClause = sql.unsafe`WHERE t.device_id = ${deviceId}`;

      if (status && status !== 'all') {
        whereClause = sql.unsafe`${whereClause} AND t.status = ${status}`;
      }

      // 获取总数
      const countResult = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT COUNT(*) as total
        FROM saito_gateway.tasks t
        ${whereClause}
      `);

      const total = parseInt(countResult.rows[0].total, 10);

      // 获取任务列表
      const result = await this.persistentService.pgPool.query(
        sql.type(DeviceTasksResultSchema)`
          SELECT
            t.id,
            t.device_id,
            t.model,
            t.created_at,
            t.status,
            t.total_duration,
            t.load_duration,
            t.prompt_eval_count,
            t.prompt_eval_duration,
            t.eval_count,
            t.eval_duration,
            t.updated_at
          FROM
            saito_gateway.tasks t
          ${whereClause}
          ORDER BY
            t.created_at DESC
          LIMIT
            ${pageSize}
          OFFSET
            ${(page - 1) * pageSize}
        `
      );

      return {
        data: result.rows,
        total,
        page,
        pageSize
      };
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取设备任务列表失败: ${errorMessage}`);
      throw error;
    }
  }



  /**
   * Register device with code
   */
  async registerDevice(
    code: string,
    gatewayAddress: string,
    rewardAddress: string,
    deviceType: string | null,
    _gpuType: string | null, // 参数名前加下划线表示未使用，保留参数以兼容现有调用
    ipAddress: string | null,
    localModels: any = null
  ) {
    try {
      // 获取连接任务
      const connectTask = await this.getConnectTaskByCode(code);

      if (!connectTask) {
        throw new Error('Invalid code');
      }

      // 使用事务确保数据一致性
      return await this.transaction(async (conn) => {
        // 获取设备当前状态
        const deviceResult = await conn.query(sql.unsafe`
          SELECT status FROM saito_gateway.devices WHERE id = ${connectTask.device_id}
        `);

        if (deviceResult.rows.length === 0) {
          throw new Error(`Device not found: ${connectTask.device_id}`);
        }

        const currentStatus = deviceResult.rows[0].status;

        // 更新设备信息
        await conn.query(sql.unsafe`
          UPDATE saito_gateway.devices
          SET
            reward_address = ${rewardAddress},
            device_type = ${deviceType},
            ip_address = ${ipAddress},
            status = 'connected'
          WHERE id = ${connectTask.device_id}
        `);

        // 如果状态发生变化，记录状态变更
        if (currentStatus !== 'connected') {
          await conn.query(sql.unsafe`
            INSERT INTO saito_gateway.device_status_changes (
              device_id,
              from_status,
              to_status,
              change_time,
              date
            )
            VALUES (
              ${connectTask.device_id},
              ${currentStatus},
              'connected',
              NOW(),
              CURRENT_DATE
            )
          `);
        }

        // 更新连接任务状态
        await conn.query(sql.unsafe`
          UPDATE saito_gateway.connect_tasks
          SET
            status = 'connected',
            gateway_address = ${gatewayAddress}
          WHERE id = ${connectTask.id}
        `);
        // 如果提供了本地模型信息，保存到设备模型表
        if (localModels && localModels.models && Array.isArray(localModels.models)) {
          try {
            // 先删除该设备的所有现有模型记录
            await conn.query(sql.unsafe`
              DELETE FROM saito_gateway.device_models
              WHERE device_id = ${connectTask.device_id}
            `);


            // 插入新的模型记录
            for (const model of localModels.models) {

              await conn.query(sql.unsafe`
                INSERT INTO saito_gateway.device_models (
                  device_id,
                  model_name,
                  model_family,
                  parameter_size,
                  quantization_level,
                  format,
                  size_bytes,
                  digest,
                  modified_at
                )
                VALUES (
                  ${connectTask.device_id},
                  ${model.name},
                  ${model.details?.family || null},
                  ${model.details?.parameter_size || null},
                  ${model.details?.quantization_level || null},
                  ${model.details?.format || null},
                  ${model.size || null},
                  ${model.digest || null},
                  ${model.modified_at ? model.modified_at : null}
                )
              `);
            }
          } catch (modelError: unknown) {
            // 如果表不存在，记录错误但不中断注册流程
            this.logger.warn(`Error updating device models: ${modelError instanceof Error ? modelError.message : String(modelError)}`);

            // 继续执行，不要中断注册流程
          }
        }

        return {
          node_id: connectTask.device_id,
          status: 'connected',
          device_type: deviceType,
          reward_address: rewardAddress
        };
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`注册设备失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get device by code
   */
  async getDeviceByCode(code: string) {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(DeviceByCodeResultSchema)`
          SELECT
            d.*
          FROM
            saito_gateway.devices d
          JOIN
            saito_gateway.connect_tasks ct ON d.id = ct.node_id
          WHERE
            ct.one_time_code = ${code}
        `
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`通过代码获取设备失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get device by ID without requiring user ID
   */
  async getDeviceById(deviceId: string) {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(SimpleDeviceResultSchema)`
          SELECT
            id,
            status,
            name,
            device_type,
            gpu_model,
            last_ping
          FROM
            saito_gateway.devices
          WHERE
            id = ${deviceId}
        `
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取设备失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get connect task by ID
   */
  async getConnectTaskById(taskId: string, userId: string) {
    try {
      // Build the WHERE clause based on whether userId is provided
      let whereClause = sql.unsafe`WHERE id = ${taskId}`;

      // Only filter by userId if it's provided and not empty
      if (userId && userId.trim() !== '') {
        whereClause = sql.unsafe`${whereClause} AND user_id = ${userId}`;
      }

      const result = await this.persistentService.pgPool.query(
        sql.type(ConnectTaskByIdResultSchema)`
          SELECT
            id as task_id,
            status,
            node_id as device_id,
            created_at,
            updated_at
          FROM
            saito_gateway.connect_tasks
          ${whereClause}
        `
      );

      if (result.rows.length === 0) {
        return null;
      }

      return result.rows[0];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取连接任务失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Update device detailed information
   */
  async updateDeviceDetailedInfo(
    deviceId: string,
    cpuModel: string | null,
    cpuCores: number | null,
    cpuThreads: number | null,
    ramTotal: number | null,
    ramAvailable: number | null,
    gpuModel: string | null,
    gpuCount: number | null,
    gpuMemory: number | null,
    gpuTemperature: number | null,
    diskTotal: number | null,
    diskAvailable: number | null,
    osInfo: string | null,
    deviceType: string | null
  ) {
    try {
      await this.persistentService.pgPool.query(sql.unsafe`
        UPDATE saito_gateway.devices
        SET
          cpu_model = ${cpuModel},
          cpu_cores = ${cpuCores},
          cpu_threads = ${cpuThreads},
          ram_total = ${ramTotal},
          ram_available = ${ramAvailable},
          gpu_model = ${gpuModel},
          gpu_count = ${gpuCount},
          gpu_memory = ${gpuMemory},
          gpu_temperature = ${gpuTemperature},
          disk_total = ${diskTotal},
          disk_available = ${diskAvailable},
          os_info = ${osInfo},
          device_type = ${deviceType}
        WHERE id = ${deviceId}
      `);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`更新设备详细信息失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Update device IP address
   */
  async updateDeviceIpAddress(deviceId: string, ipAddress: string) {
    try {
      await this.persistentService.pgPool.query(sql.unsafe`
        UPDATE saito_gateway.devices
        SET ip_address = ${ipAddress}
        WHERE id = ${deviceId}
      `);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`更新设备IP地址失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Check if user exists
   */
  async checkUserExists(userId: string): Promise<boolean> {
    try {
      const result = await this.persistentService.pgPool.query(sql.unsafe`
        SELECT id FROM saito_gateway.users WHERE id = ${userId} LIMIT 1
      `);
      return result.rows.length > 0;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`检查用户是否存在失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Create user if not exists
   */
  async createUserIfNotExists(userId: string, walletAddress: string): Promise<boolean> {
    try {
      // First check if user already exists
      const userExists = await this.checkUserExists(userId);
      if (userExists) {
        return true;
      }

      // Create the user if it doesn't exist
      await this.persistentService.pgPool.query(sql.unsafe`
        INSERT INTO saito_gateway.users (
          id,
          wallet_address,
          username,
          created_at,
          updated_at
        )
        VALUES (
          ${userId},
          ${walletAddress},
          ${'user_' + walletAddress.substring(0, 8)},
          NOW(),
          NOW()
        )
        ON CONFLICT (id) DO NOTHING
      `);

      return true;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`创建用户失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get device models
   */
  async getDeviceModels(deviceId: string) {
    try {
      // 使用 DeviceModelSchema 作为查询结果的类型
      // 只返回在线设备的模型，不再过滤用户ID
      const result = await this.persistentService.pgPool.query(
        sql.type(DeviceModelSchema)`
          SELECT
            dm.id,
            dm.device_id,
            dm.model_name,
            dm.model_family,
            dm.parameter_size,
            dm.quantization_level,
            dm.format,
            dm.size_bytes,
            dm.digest,
            dm.modified_at,
            dm.created_at,
            dm.updated_at
          FROM
            saito_gateway.device_models dm
          JOIN
            saito_gateway.devices d ON dm.device_id = d.id
          WHERE
            dm.device_id = ${deviceId}
            AND d.status = 'connected'
          ORDER BY
            dm.model_name ASC
        `
      );

      return result.rows;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // 检查是否是表不存在的错误
      if (errorMessage.includes('relation "saito_gateway.device_models" does not exist')) {

        // 返回空数组而不是抛出错误
        return [];
      }

      this.logger.error(`获取设备模型失败: ${errorMessage}`);
      throw error;
    }
  }

  async getModels() {
    try {
      // 使用 DeviceModelSchema 作为查询结果的类型
      // 只返回在线设备的模型
      const result = await this.persistentService.pgPool.query(
        sql.type(DeviceModelSchema)`
          SELECT DISTINCT ON (dm.model_name)
            dm.id,
            dm.device_id,
            dm.model_name,
            dm.model_family,
            dm.parameter_size,
            dm.quantization_level,
            dm.format,
            dm.size_bytes,
            dm.digest,
            dm.modified_at,
            dm.created_at,
            dm.updated_at
          FROM
            saito_gateway.device_models dm
          JOIN
            saito_gateway.devices d ON dm.device_id = d.id
          WHERE
            d.status = 'connected'
          ORDER BY
            dm.model_name ASC,
            dm.created_at DESC
        `
      );

      return result.rows;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // 检查是否是表不存在的错误
      if (errorMessage.includes('relation "saito_gateway.device_models" does not exist')) {

        // 返回空数组而不是抛出错误
        return [];
      }

      this.logger.error(`获取模型失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Update device models
   * @param deviceId Device ID
   * @param models List of models supported by the device
   */
  async updateDeviceModels(deviceId: string, models: any[]) {
    try {
      // 使用事务确保数据一致性
      await this.transaction(async (conn) => {
        // 首先删除设备的所有现有模型
        await conn.query(sql.unsafe`
          DELETE FROM saito_gateway.device_models
          WHERE device_id = ${deviceId}
        `);

        // 然后插入新的模型
        for (const model of models) {
          await conn.query(sql.unsafe`
            INSERT INTO saito_gateway.device_models (
              device_id,
              model_name,
              model_family,
              parameter_size,
              quantization_level,
              format,
              size_bytes,
              digest,
              modified_at
            )
            VALUES (
              ${deviceId},
              ${model.name},
              ${model.details?.family || null},
              ${model.details?.parameter_size || null},
              ${model.details?.quantization_level || null},
              ${model.details?.format || null},
              ${model.size || null},
              ${model.digest || null},
              ${model.modified_at ? model.modified_at : null}
            )
          `);
        }
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);

      // 检查是否是表不存在的错误
      if (errorMessage.includes('relation "saito_gateway.device_models" does not exist')) {
        this.logger.error(`设备模型表不存在: ${errorMessage}`);
        throw new Error('Device models table does not exist');
      }

      this.logger.error(`更新设备模型失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get the latest device metrics
   * @param deviceId Device ID
   * @returns Latest device metrics or null if not found
   */
  async getLatestDeviceMetrics(deviceId: string): Promise<z.infer<typeof ChatDeviceMetricsSchema> | null> {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(ChatDeviceMetricsSchema)`
          SELECT *
          FROM saito_gateway.device_metrics
          WHERE device_id = ${deviceId}
          ORDER BY timestamp DESC
          LIMIT 1
        `
      );

      if (R.isEmpty(result.rows)) {
        return null;
      }

      return result.rows[0];
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取设备最新指标失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get the user ID associated with a device
   * @param deviceId Device ID
   * @returns User ID
   */
  async getUserOfDevice(deviceId: string): Promise<string> {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(z.object({ user_id: z.string().uuid() }))`
          SELECT user_id FROM saito_gateway.devices WHERE id = ${deviceId}
        `
      );

      if (R.isEmpty(result.rows)) {
        throw new Error(`Device not found: ${deviceId}`);
      }

      return result.rows[0].user_id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取设备用户ID失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get the user ID associated with a connect task
   * @param deviceId Device ID
   * @returns User ID
   */
  async getUserOfConnectTask(deviceId: string): Promise<string> {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(z.object({ user_id: z.string().uuid() }))`
          SELECT user_id FROM saito_gateway.connect_tasks WHERE node_id = ${deviceId}
        `
      );

      if (R.isEmpty(result.rows)) {
        throw new Error(`Connect task not found for device: ${deviceId}`);
      }

      return result.rows[0].user_id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取连接任务用户ID失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Get the device ID associated with a node ID
   * @param nodeId Node ID
   * @returns Device ID
   */
  async getDeviceIdOfNodeId(nodeId: string): Promise<string> {
    try {
      const result = await this.persistentService.pgPool.query(
        sql.type(z.object({ id: z.string().uuid() }))`
          SELECT id FROM saito_gateway.devices WHERE node_id = ${nodeId}
        `
      );

      if (R.isEmpty(result.rows)) {
        throw new Error(`Device not found for node: ${nodeId}`);
      }

      return result.rows[0].id;
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`获取节点设备ID失败: ${errorMessage}`);
      throw error;
    }
  }

  /**
   * Find available nodes for task distribution
   * Implements a node selection algorithm that:
   * 1. First tries to find idle nodes (nodes without running tasks)
   * 2. If no idle nodes are found, falls back to any online node
   * 3. If a model is specified, only returns nodes that support that model
   *
   * @param model Optional model name to filter nodes by supported models
   * @returns Array of available nodes
   */
  async findAvailableNodes(model?: string): Promise<Array<{node_id: string, id: string}>> {
    try {
      // First try to find idle nodes that support the requested model (if specified)
      if (model) {
        try {
          // Find idle nodes that support the requested model
          const idleNodesResult = await this.persistentService.pgPool.query(
            sql.type(IdleNodesResultSchema)`
              SELECT DISTINCT
                d.id as node_id,
                d.id,
                d.last_ping
              FROM
                saito_gateway.devices d
              JOIN
                saito_gateway.device_models dm ON d.id = dm.device_id
              WHERE
                d.status = 'connected'
                AND dm.model_name = ${model}
                AND NOT EXISTS (
                  SELECT 1 FROM saito_gateway.tasks t
                  WHERE t.device_id = d.id AND t.status = 'running'
                )
              ORDER BY
                d.last_ping DESC
              LIMIT 5
            `
          );

          // If we found idle nodes, return them
          if (idleNodesResult.rows.length > 0) {
            this.logger.log(`Found ${idleNodesResult.rows.length} idle nodes supporting model ${model}`);
            return [...idleNodesResult.rows];
          }

          // If no idle nodes were found, try to find any online node that supports the model
          const anyNodesResult = await this.persistentService.pgPool.query(
            sql.type(IdleNodesResultSchema)`
              SELECT
                d.id as node_id,
                d.id,
                d.last_ping,
                RANDOM() as random_order
              FROM
                saito_gateway.devices d
              JOIN
                saito_gateway.device_models dm ON d.id = dm.device_id
              WHERE
                d.status = 'connected'
                AND dm.model_name = ${model}
              ORDER BY
                random_order
              LIMIT 5
            `
          );

          if (anyNodesResult.rows.length > 0) {
            this.logger.log(`Found ${anyNodesResult.rows.length} online nodes supporting model ${model}`);
            return [...anyNodesResult.rows];
          }

          // If we still haven't found any nodes, return an empty array
          this.logger.warn(`No nodes found supporting model ${model}`);
          return [];
        } catch (modelError: unknown) {
          const modelErrorMessage = modelError instanceof Error ? modelError.message : String(modelError);

          // If the device_models table doesn't exist, fall back to not filtering by model
          if (modelErrorMessage.includes('relation "saito_gateway.device_models" does not exist')) {
            this.logger.warn(`Device models table does not exist, falling back to not filtering by model`);

            // Try to find any idle nodes without filtering by model
            return this.findAvailableNodes();
          }

          // For other errors, rethrow
          throw modelError;
        }
      } else {
        // No model specified, try to find any idle nodes
        const idleNodesResult = await this.persistentService.pgPool.query(
          sql.type(IdleNodesResultSchema)`
            SELECT
              d.id as node_id,
              d.id,
              d.last_ping
            FROM
              saito_gateway.devices d
            WHERE
              d.status = 'connected'
              AND NOT EXISTS (
                SELECT 1 FROM saito_gateway.tasks t
                WHERE t.device_id = d.id AND t.status = 'running'
              )
            ORDER BY
              d.last_ping DESC
            LIMIT 5
          `
        );

        // If we found idle nodes, return them
        if (idleNodesResult.rows.length > 0) {
          this.logger.log(`Found ${idleNodesResult.rows.length} idle nodes`);
          return [...idleNodesResult.rows];
        }

        // If no idle nodes were found, try to find any online node
        const anyNodesResult = await this.persistentService.pgPool.query(
          sql.type(IdleNodesResultSchema)`
            SELECT
              d.id as node_id,
              d.id,
              d.last_ping,
              RANDOM() as random_order
            FROM
              saito_gateway.devices d
            WHERE
              d.status = 'connected'
            ORDER BY
              random_order
            LIMIT 5
          `
        );

        if (anyNodesResult.rows.length > 0) {
          this.logger.log(`Found ${anyNodesResult.rows.length} online nodes`);
          return [...anyNodesResult.rows];
        }

        // If we still haven't found any nodes, return an empty array
        this.logger.warn(`No online nodes found`);
        return [];
      }
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      this.logger.error(`查找可用节点失败: ${errorMessage}`);
      throw error;
    }
  }
}
