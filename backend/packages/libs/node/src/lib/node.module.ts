import { Module, Global } from '@nestjs/common';
import { NodeServiceProvider } from "./node.service";
import { NodeRepository } from "./node.repository";
import { PersistentModule } from '@saito/persistent';
import { RedisModule } from '@saito/redis';
import { DeviceService } from './device.service';
import { ConnectTaskService } from './connect-task.service';
import { DeviceHeartbeatService } from './device-heartbeat.service';
import { DeviceModelService } from './device-model.service';
import { NodeSelectionService } from './node-selection.service';

@Global()
@Module({
  imports: [
    PersistentModule,
    RedisModule
  ],
  providers: [
    NodeServiceProvider,
    NodeRepository,
    {
      provide: 'NodeRepository',
      useExisting: NodeRepository
    },
    DeviceService,
    ConnectTaskService,
    DeviceHeartbeatService,
    DeviceModelService,
    NodeSelectionService
  ],
  exports: [NodeServiceProvider, NodeRepository]
})
export class NodeModule {}
