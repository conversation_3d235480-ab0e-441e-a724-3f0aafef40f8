import { Injectable, Inject, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { NodeRepository } from "./node.repository";
import {
  DeviceModel,
  DeviceModelReportRequest,
  DeviceModelReportResponse
} from '@saito/models';

/**
 * 设备模型服务 - 处理设备模型相关操作
 */
@Injectable()
export class DeviceModelService {
  private readonly logger = new Logger(DeviceModelService.name);

  constructor(
    @Inject(NodeRepository)
    private readonly nodeRepository: NodeRepository
  ) {}

  /**
   * Get models
   */
  async getModels(): Promise<DeviceModel[]> {
    try {
      // Get the device models
      const models = await this.nodeRepository.getModels();
      return [...models]; // Convert readonly array to mutable array
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException('Failed to get models', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Get online device models
   * @param format The format of the response, either 'ollama' or 'openai'
   * @returns The list of models from online devices in the specified format
   */
  async getOnlineDeviceModels(format: string = 'ollama'): Promise<any> {
    try {
      // Get all connected nodes
      const connectedNodes = await this.nodeRepository.findIdleNodes();

      if (connectedNodes.length === 0) {
        return format === 'openai'
          ? { object: 'list', data: [] }
          : { models: [] };
      }

      // Get models from all connected nodes
      const allModels: DeviceModel[] = [];
      for (const node of connectedNodes) {
        try {
          const models = await this.nodeRepository.getDeviceModels(node.id);
          allModels.push(...models);
        } catch (error) {
          this.logger.error(`Failed to get models for device ${node.id}: ${error}`);
        }
      }

      // Remove duplicates based on model_name
      const uniqueModels = allModels.filter((model, index, self) =>
        index === self.findIndex((m) => m.model_name === model.model_name)
      );

      // Format the response based on the requested format
      if (format === 'openai') {
        // OpenAI format
        return {
          object: 'list',
          data: uniqueModels.map(model => ({
            id: model.model_name,
            object: 'model',
            created: new Date(model.created_at).getTime() / 1000, // Convert to Unix timestamp
            owned_by: 'saito'
          }))
        };
      } else {
        // Ollama format
        return {
          models: uniqueModels.map(model => ({
            name: model.model_name,
            modified_at: model.modified_at || new Date(model.updated_at).toISOString(),
            size: model.size_bytes,
            model: model.model_name,
            digest: model.digest,
            details: {
              format: model.format || 'unknown',
              family: model.model_family || 'unknown',
              families: [],
              parameter_size: model.parameter_size || 'unknown',
              quantization_level: model.quantization_level || 'unknown'
            }
          }))
        };
      }
    } catch (error) {
      this.logger.error(`Failed to get online device models: ${error}`);
      throw new HttpException('Failed to get online device models', HttpStatus.INTERNAL_SERVER_ERROR);
    }
  }

  /**
   * Report device models
   * @param request The request containing the device ID and models
   * @returns The response indicating success or failure
   */
  async reportDeviceModels(request: DeviceModelReportRequest): Promise<DeviceModelReportResponse> {
    try {
      this.logger.log(`Reporting device models for device ${request.device_id}`);

      // Check if the device exists
      const device = await this.nodeRepository.getDeviceById(request.device_id);
      if (!device) {
        this.logger.warn(`Device ${request.device_id} not found`);
        return {
          success: false,
          message: `Device with ID ${request.device_id} not found`
        };
      }

      // Update the device models
      await this.nodeRepository.updateDeviceModels(request.device_id, request.models);

      this.logger.log(`Successfully updated models for device ${request.device_id}`);
      return {
        success: true,
        message: 'Device models updated successfully'
      };
    } catch (error: any) {
      this.logger.error(`Error reporting device models: ${error.message}`);
      return {
        success: false,
        message: `Error updating device models: ${error.message}`
      };
    }
  }
}
