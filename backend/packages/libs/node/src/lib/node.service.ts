import { Injectable, Logger } from '@nestjs/common';
import { NodeService } from "./node.interface";
import {
  Device,
  CreateConnectTaskRequest,
  CreateConnectTaskResponse,
  DeviceHeartbeatRequest,
  DeviceListRequest,
  DeviceListResponse,
  DeviceConnectionRequest,
  DeviceConnectionResponse,
  DeviceStatusChange,
  Task,
  DeviceRegisterRequest,
  DeviceRegisterResponse,
  NewDeviceHeartbeatRequest,
  DeviceModel,
  ChatNode,
  DeviceModelReportRequest,
  DeviceModelReportResponse
} from '@saito/models';
import { DeviceService } from './device.service';
import { ConnectTaskService } from './connect-task.service';
import { DeviceHeartbeatService } from './device-heartbeat.service';
import { DeviceModelService } from './device-model.service';
import { NodeSelectionService } from './node-selection.service';

/**
 * NodeServiceImpl - 门面服务，将请求委托给各个专门的服务
 */
@Injectable()
export class NodeServiceImpl implements NodeService {
  private readonly logger = new Logger(NodeServiceImpl.name);

  constructor(
    private readonly deviceService: DeviceService,
    private readonly connectTaskService: ConnectTaskService,
    private readonly deviceHeartbeatService: DeviceHeartbeatService,
    private readonly deviceModelService: DeviceModelService,
    private readonly nodeSelectionService: NodeSelectionService
  ) {}

  /**
   * Create a new connect task for a device
   */
  async createConnectTask(
    request: CreateConnectTaskRequest,
    userId: string,
    walletAddress: string
  ): Promise<CreateConnectTaskResponse> {
    return this.connectTaskService.createConnectTask(request, userId, walletAddress);
  }

  /**
   * Get connect tasks for a user
   */
  async getConnectTasks(
    request: DeviceListRequest,
    userId: string
  ): Promise<DeviceListResponse> {
    return this.connectTaskService.getConnectTasks(request, userId);
  }

  /**
   * Connect a device using a one-time code
   */
  async connectDevice(
    request: DeviceConnectionRequest
  ): Promise<DeviceConnectionResponse> {
    return this.connectTaskService.connectDevice(request);
  }

  /**
   * Update device heartbeat
   */
  async updateDeviceHeartbeat(
    request: DeviceHeartbeatRequest
  ): Promise<void> {
    return this.deviceHeartbeatService.updateDeviceHeartbeat(request);
  }

  /**
   * 检查设备状态，每5秒执行一次
   * 如果设备2分钟内没有心跳，则标记为断开连接
   */
  async checkDevicesStatus() {
    return this.deviceHeartbeatService.checkDevicesStatus();
  }

  /**
   * 检查连接任务超时，每分钟执行一次
   * 如果连接任务30分钟内未完成，则标记为失败
   */
  async checkConnectTasksTimeout() {
    return this.connectTaskService.checkConnectTasksTimeout();
  }

  /**
   * Get devices for a user
   */
  async getDevices(
    request: DeviceListRequest,
    userId: string
  ): Promise<DeviceListResponse> {
    return this.deviceService.getDevices(request, userId);
  }

  /**
   * Get device by ID
   */
  async getDevice(
    deviceId: string,
    userId: string
  ): Promise<Device | null> {
    return this.deviceService.getDevice(deviceId, userId);
  }

  /**
   * Find idle nodes
   * @param model Optional model name to filter nodes by supported models
   */
  async findIdleNodes(model?: string): Promise<Array<{node_id: string, id: string}>> {
    return this.nodeSelectionService.findIdleNodes(model);
  }

  /**
   * Find nodes that are not busy with tasks
   * @param model Optional model name to filter nodes by supported models
   * @returns Array of nodes that are not busy, or if none found, any online nodes that support the model
   */
  async findAvailableNodes(model?: string): Promise<Array<{node_id: string, id: string}>> {
    return this.nodeSelectionService.findAvailableNodes(model);
  }

  /**
   * Find nodes by status
   * @param status Status to filter by
   * @param model Optional model name to filter nodes by supported models
   * @returns Array of nodes with the specified status
   */
  async findNodesByStatus(status: string, model?: string): Promise<Array<{node_id: string, id: string}>> {
    return this.nodeSelectionService.findNodesByStatus(status, model);
  }

  /**
   * Get device status history
   */
  async getDeviceStatusHistory(deviceId: string, userId: string, days: number): Promise<DeviceStatusChange[]> {
    return this.deviceService.getDeviceStatusHistory(deviceId, userId, days);
  }

  /**
   * Get device tasks
   */
  async getDeviceTasks(deviceId: string, userId: string, request: DeviceListRequest): Promise<{data: Task[], total: number, page: number, pageSize: number}> {
    return this.deviceService.getDeviceTasks(deviceId, userId, request);
  }

  /**
   * Register device
   */
  async registerDevice(request: DeviceRegisterRequest): Promise<DeviceRegisterResponse> {
    return this.deviceService.registerDevice(request);
  }

  /**
   * Update device heartbeat with new format
   */
  async updateDeviceHeartbeatNew(request: NewDeviceHeartbeatRequest): Promise<void> {
    return this.deviceHeartbeatService.updateDeviceHeartbeatNew(request);
  }

  /**
   * Get connect task status
   */
  async getConnectTaskStatus(taskId: string, userId: string): Promise<{task_id: string, status: string, device_id: string, created_at: string, updated_at: string} | null> {
    return this.connectTaskService.getConnectTaskStatus(taskId, userId);
  }

  /**
   * Get device models
   */
  async getDeviceModels(deviceId: string, userId: string): Promise<DeviceModel[]> {
    return this.deviceService.getDeviceModels(deviceId, userId);
  }

  /**
   * Get models
   */
  async getModels(): Promise<DeviceModel[]> {
    return this.deviceModelService.getModels();
  }

  /**
   * Get online device models
   * @param format The format of the response, either 'ollama' or 'openai'
   * @returns The list of models from online devices in the specified format
   */
  async getOnlineDeviceModels(format: string = 'ollama'): Promise<any> {
    return this.deviceModelService.getOnlineDeviceModels(format);
  }

  /**
   * Report device models
   * @param request The request containing the device ID and models
   * @returns The response indicating success or failure
   */
  async reportDeviceModels(request: DeviceModelReportRequest): Promise<DeviceModelReportResponse> {
    return this.deviceModelService.reportDeviceModels(request);
  }

  /**
   * 获取最佳可用节点
   * 根据设备状态和性能指标选择最佳节点
   * @param model 可选的模型名称，用于筛选支持该模型的节点
   * @returns 最佳节点或错误对象
   */
  public async getTargetNode(model?: string): Promise<ChatNode | { error: { message: string; type: string; param: string | null; code: string | null; }; status: number; }> {
    return this.nodeSelectionService.getTargetNode(model);
  }
}

export const NodeServiceProvider = {
  provide: NodeService,
  useClass: NodeServiceImpl,
};
