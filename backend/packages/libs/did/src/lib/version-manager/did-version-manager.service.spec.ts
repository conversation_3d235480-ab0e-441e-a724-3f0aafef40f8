import { Test, TestingModule } from '@nestjs/testing';
import { DidVersionManagerService } from './did-version-manager.service';
import { RawDidDocument } from '@saito/models';

describe('DidVersionManagerService', () => {
  let service: DidVersionManagerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [DidVersionManagerService],
    }).compile();

    service = module.get<DidVersionManagerService>(DidVersionManagerService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('calculateDocumentHash', () => {
    it('should calculate consistent hash for same document', () => {
      const document: RawDidDocument = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [{
          id: 'did:sight:hoster:test123#key-1',
          type: 'Ed25519VerificationKey2020',
          controller: 'did:sight:hoster:test123',
          publicKeyMultibase: 'z6MkhaXgBZDvotDkL5257faiztiGiC2QtKLGpbnnEGta2doK'
        }],
        authentication: ['did:sight:hoster:test123#key-1'],
        service: [],
        'sight:seq': 1
      };

      const hash1 = service.calculateDocumentHash(document);
      const hash2 = service.calculateDocumentHash(document);

      expect(hash1).toBe(hash2);
      expect(hash1).toMatch(/^[a-f0-9]{64}$/); // SHA-256 hex format
    });

    it('should exclude specified fields from hash calculation', () => {
      const document: RawDidDocument = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [],
        authentication: [],
        service: [],
        'sight:seq': 1,
        'sight:hash': 'old-hash',
        proof: { type: 'Ed25519Signature2020', proofValue: 'signature' }
      };

      const hash = service.calculateDocumentHash(document);
      
      // 修改被排除的字段不应该影响哈希
      const modifiedDoc = {
        ...document,
        'sight:hash': 'new-hash',
        proof: { type: 'Ed25519Signature2020', proofValue: 'different-signature' }
      };

      const hashAfterModification = service.calculateDocumentHash(modifiedDoc);
      expect(hash).toBe(hashAfterModification);
    });
  });

  describe('generateNextSequence', () => {
    it('should generate sequence 1 for undefined input', () => {
      expect(service.generateNextSequence()).toBe(1);
      expect(service.generateNextSequence(undefined)).toBe(1);
    });

    it('should increment numeric sequence', () => {
      expect(service.generateNextSequence(5)).toBe(6);
      expect(service.generateNextSequence(0)).toBe(1);
    });

    it('should handle string sequence numbers', () => {
      expect(service.generateNextSequence('10')).toBe(11);
      expect(service.generateNextSequence('invalid')).toBe(1);
    });
  });

  describe('compareVersions', () => {
    const createTestDoc = (seq: number, hash?: string): RawDidDocument => ({
      '@context': ['https://www.w3.org/ns/did/v1'],
      id: 'did:sight:hoster:test123',
      verificationMethod: [],
      authentication: [],
      service: [],
      'sight:seq': seq,
      'sight:hash': hash || 'test-hash'
    });

    it('should compare by sequence number', () => {
      const doc1 = createTestDoc(2);
      const doc2 = createTestDoc(1);

      expect(service.compareVersions(doc1, doc2)).toBe(1);
      expect(service.compareVersions(doc2, doc1)).toBe(-1);
      expect(service.compareVersions(doc1, doc1)).toBe(0);
    });

    it('should compare by hash when sequences are equal', () => {
      const doc1 = createTestDoc(1, 'hash-a');
      const doc2 = createTestDoc(1, 'hash-b');

      // 当序列号相同但哈希不同时，应该使用时间戳比较
      const result = service.compareVersions(doc1, doc2);
      expect(typeof result).toBe('number');
    });
  });

  describe('needsUpdate', () => {
    it('should return true when new document has higher sequence', () => {
      const currentDoc = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [],
        authentication: [],
        service: [],
        'sight:seq': 1
      } as RawDidDocument;

      const newDoc = {
        ...currentDoc,
        'sight:seq': 2
      };

      expect(service.needsUpdate(currentDoc, newDoc)).toBe(true);
    });

    it('should return false when new document has lower sequence', () => {
      const currentDoc = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [],
        authentication: [],
        service: [],
        'sight:seq': 2
      } as RawDidDocument;

      const newDoc = {
        ...currentDoc,
        'sight:seq': 1
      };

      expect(service.needsUpdate(currentDoc, newDoc)).toBe(false);
    });
  });

  describe('updateVersionInfo', () => {
    it('should increment sequence number when requested', () => {
      const document: RawDidDocument = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [],
        authentication: [],
        service: [],
        'sight:seq': 5
      };

      const updated = service.updateVersionInfo(document, true);

      expect(updated['sight:seq']).toBe(6);
      expect(updated['sight:updatedAt']).toBeDefined();
      expect(updated['sight:hash']).toBeDefined();
    });

    it('should not increment sequence when not requested', () => {
      const document: RawDidDocument = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [],
        authentication: [],
        service: [],
        'sight:seq': 5
      };

      const updated = service.updateVersionInfo(document, false);

      expect(updated['sight:seq']).toBe(5);
      expect(updated['sight:hash']).toBeDefined();
    });
  });

  describe('validateVersionIntegrity', () => {
    it('should validate correct document', () => {
      const document: RawDidDocument = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [],
        authentication: [],
        service: [],
        'sight:seq': 1
      };

      // 计算正确的哈希
      const hash = service.calculateDocumentHash(document);
      document['sight:hash'] = hash;

      const result = service.validateVersionIntegrity(document);

      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing sequence number', () => {
      const document: RawDidDocument = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [],
        authentication: [],
        service: []
      };

      const result = service.validateVersionIntegrity(document);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing sequence number (sight:seq)');
    });

    it('should detect hash mismatch', () => {
      const document: RawDidDocument = {
        '@context': ['https://www.w3.org/ns/did/v1'],
        id: 'did:sight:hoster:test123',
        verificationMethod: [],
        authentication: [],
        service: [],
        'sight:seq': 1,
        'sight:hash': 'wrong-hash'
      };

      const result = service.validateVersionIntegrity(document);

      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Document hash mismatch - document may have been tampered with');
    });
  });
});
