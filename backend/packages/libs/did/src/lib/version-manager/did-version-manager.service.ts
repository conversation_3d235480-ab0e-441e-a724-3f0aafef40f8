import { Injectable, Logger } from '@nestjs/common';
import { RawDidDocument } from '@saito/models';
import * as crypto from 'crypto';
import stringify from 'fast-json-stable-stringify';

/**
 * DID文档版本管理服务
 * 负责管理DID文档的版本控制、哈希计算和增量更新
 */
@Injectable()
export class DidVersionManagerService {
  private readonly logger = new Logger(DidVersionManagerService.name);

  /**
   * 计算DID文档的哈希值
   * @param document DID文档
   * @param excludeFields 要排除的字段
   * @returns 文档哈希值
   */
  calculateDocumentHash(document: RawDidDocument, excludeFields: string[] = ['sight:hash', 'proof']): string {
    try {
      // 创建文档副本并移除指定字段
      const docCopy = { ...document };
      excludeFields.forEach(field => {
        delete (docCopy as any)[field];
      });

      // 对对象进行深度排序以确保一致的哈希
      const sortedDoc = this.deepSortObject(docCopy);
      
      // 生成稳定的JSON字符串
      const jsonString = stringify(sortedDoc);
      
      // 计算SHA-256哈希
      const hash = crypto.createHash('sha256').update(jsonString, 'utf8').digest('hex');
      
      this.logger.debug(`Calculated document hash: ${hash.substring(0, 16)}...`);
      return hash;
    } catch (error) {
      this.logger.error(`Failed to calculate document hash: ${error}`);
      throw new Error(`Hash calculation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 生成新的序列号
   * @param currentSeq 当前序列号
   * @returns 新的序列号
   */
  generateNextSequence(currentSeq?: number | string): number {
    if (currentSeq === undefined || currentSeq === null) {
      return 1;
    }

    const numSeq = typeof currentSeq === 'string' ? parseInt(currentSeq, 10) : currentSeq;
    return isNaN(numSeq) ? 1 : numSeq + 1;
  }

  /**
   * 比较两个DID文档的版本
   * @param doc1 文档1
   * @param doc2 文档2
   * @returns 比较结果：1表示doc1更新，-1表示doc2更新，0表示相同
   */
  compareVersions(doc1: RawDidDocument, doc2: RawDidDocument): number {
    const seq1 = this.extractSequence(doc1);
    const seq2 = this.extractSequence(doc2);

    if (seq1 > seq2) return 1;
    if (seq1 < seq2) return -1;

    // 序列号相同时比较哈希
    const hash1 = doc1['sight:hash'] || this.calculateDocumentHash(doc1);
    const hash2 = doc2['sight:hash'] || this.calculateDocumentHash(doc2);

    if (hash1 === hash2) return 0;
    
    // 如果哈希不同但序列号相同，可能是并发更新，使用时间戳或其他策略
    const created1 = this.extractCreatedTime(doc1);
    const created2 = this.extractCreatedTime(doc2);

    return created1 > created2 ? 1 : -1;
  }

  /**
   * 检查文档是否需要更新
   * @param currentDoc 当前文档
   * @param newDoc 新文档
   * @returns 是否需要更新
   */
  needsUpdate(currentDoc: RawDidDocument, newDoc: RawDidDocument): boolean {
    return this.compareVersions(newDoc, currentDoc) > 0;
  }

  /**
   * 更新文档版本信息
   * @param document 要更新的文档
   * @param incrementSeq 是否增加序列号
   * @returns 更新后的文档
   */
  updateVersionInfo(document: RawDidDocument, incrementSeq: boolean = true): RawDidDocument {
    const updatedDoc = { ...document };

    // 更新序列号
    if (incrementSeq) {
      const currentSeq = this.extractSequence(document);
      updatedDoc['sight:seq'] = this.generateNextSequence(currentSeq);
    }

    // 更新时间戳
    updatedDoc['sight:updatedAt'] = new Date().toISOString();

    // 计算新的哈希值
    updatedDoc['sight:hash'] = this.calculateDocumentHash(updatedDoc);

    this.logger.debug(`Updated version info: seq=${updatedDoc['sight:seq']}, hash=${updatedDoc['sight:hash']?.substring(0, 16)}...`);
    return updatedDoc;
  }

  /**
   * 创建文档变更记录
   * @param oldDoc 旧文档
   * @param newDoc 新文档
   * @returns 变更记录
   */
  createChangeRecord(oldDoc: RawDidDocument, newDoc: RawDidDocument): DidDocumentChangeRecord {
    const changes: DidDocumentChange[] = [];

    // 比较基础字段
    this.compareField(oldDoc, newDoc, 'controller', changes);
    
    // 比较服务列表
    this.compareServices(oldDoc.service || [], newDoc.service || [], changes);
    
    // 比较验证方法
    this.compareVerificationMethods(
      oldDoc.verificationMethod || [], 
      newDoc.verificationMethod || [], 
      changes
    );

    // 比较自定义字段
    this.compareCustomFields(oldDoc, newDoc, changes);

    return {
      documentId: newDoc.id,
      oldSequence: this.extractSequence(oldDoc),
      newSequence: this.extractSequence(newDoc),
      oldHash: oldDoc['sight:hash'] || '',
      newHash: newDoc['sight:hash'] || '',
      timestamp: new Date().toISOString(),
      changes
    };
  }

  /**
   * 验证文档版本的完整性
   * @param document 要验证的文档
   * @returns 验证结果
   */
  validateVersionIntegrity(document: RawDidDocument): VersionValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // 检查必需的版本字段
    if (!document['sight:seq']) {
      errors.push('Missing sequence number (sight:seq)');
    }

    if (!document['sight:hash']) {
      warnings.push('Missing document hash (sight:hash)');
    }

    // 验证哈希值
    if (document['sight:hash']) {
      const calculatedHash = this.calculateDocumentHash(document);
      if (calculatedHash !== document['sight:hash']) {
        errors.push('Document hash mismatch - document may have been tampered with');
      }
    }

    // 验证序列号格式
    const seq = document['sight:seq'];
    if (seq !== undefined && (typeof seq !== 'number' || seq < 1)) {
      errors.push('Invalid sequence number format');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }

  /**
   * 提取文档序列号
   */
  extractSequence(document: RawDidDocument): number {
    const seq = document['sight:seq'];
    if (typeof seq === 'number') return seq;
    if (typeof seq === 'string') return parseInt(seq, 10) || 0;
    return 0;
  }

  /**
   * 提取文档创建时间
   */
  private extractCreatedTime(document: RawDidDocument): number {
    const created = document['sight:createdAt'] || document['sight:updatedAt'];
    if (typeof created === 'string') {
      return new Date(created).getTime();
    }
    return 0;
  }

  /**
   * 深度排序对象
   */
  private deepSortObject(obj: any): any {
    if (Array.isArray(obj)) {
      return obj.map(item => this.deepSortObject(item));
    }
    
    if (obj && typeof obj === 'object' && obj !== null) {
      const sortedObj: any = {};
      Object.keys(obj).sort().forEach(key => {
        sortedObj[key] = this.deepSortObject(obj[key]);
      });
      return sortedObj;
    }
    
    return obj;
  }

  /**
   * 比较字段变更
   */
  private compareField(oldDoc: any, newDoc: any, fieldName: string, changes: DidDocumentChange[]): void {
    const oldValue = oldDoc[fieldName];
    const newValue = newDoc[fieldName];

    if (oldValue !== newValue) {
      changes.push({
        type: 'field_change',
        field: fieldName,
        oldValue,
        newValue
      });
    }
  }

  /**
   * 比较服务列表变更
   */
  private compareServices(oldServices: any[], newServices: any[], changes: DidDocumentChange[]): void {
    // 简化的服务比较逻辑
    if (oldServices.length !== newServices.length) {
      changes.push({
        type: 'service_change',
        field: 'service',
        oldValue: oldServices.length,
        newValue: newServices.length
      });
    }
  }

  /**
   * 比较验证方法变更
   */
  private compareVerificationMethods(oldMethods: any[], newMethods: any[], changes: DidDocumentChange[]): void {
    if (oldMethods.length !== newMethods.length) {
      changes.push({
        type: 'verification_method_change',
        field: 'verificationMethod',
        oldValue: oldMethods.length,
        newValue: newMethods.length
      });
    }
  }

  /**
   * 比较自定义字段变更
   */
  private compareCustomFields(oldDoc: any, newDoc: any, changes: DidDocumentChange[]): void {
    const customFields = ['sight:deviceType', 'sight:capabilities', 'sight:version'];
    
    customFields.forEach(field => {
      this.compareField(oldDoc, newDoc, field, changes);
    });
  }
}

/**
 * DID文档变更记录
 */
export interface DidDocumentChangeRecord {
  documentId: string;
  oldSequence: number;
  newSequence: number;
  oldHash: string;
  newHash: string;
  timestamp: string;
  changes: DidDocumentChange[];
}

/**
 * 单个变更项
 */
export interface DidDocumentChange {
  type: 'field_change' | 'service_change' | 'verification_method_change';
  field: string;
  oldValue: any;
  newValue: any;
}

/**
 * 版本验证结果
 */
export interface VersionValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
}
