import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { RawDidDocument } from '@saito/models';
import { DidVersionManagerService, DidDocumentChangeRecord } from './did-version-manager.service';
import { EnhancedDidBuilderService, DeviceCapabilityConfig } from '../capability-builder';
import { DidLocalStorage } from '../did-document-storage/did-local.storage';
import { KeypairManagerService } from '../keypair-storage';

/**
 * DID文档生命周期管理器
 * 负责DID文档的创建、更新、版本控制和持久化
 */
@Injectable()
export class DidLifecycleManagerService implements OnModuleInit {
  private readonly logger = new Logger(DidLifecycleManagerService.name);
  private currentDocument: RawDidDocument | null = null;
  private currentRewardAddress: string | undefined;
  private currentCapabilityConfig: DeviceCapabilityConfig | undefined;
  private changeHistory: DidDocumentChangeRecord[] = [];

  constructor(
    private readonly versionManager: DidVersionManagerService,
    private readonly enhancedBuilder: EnhancedDidBuilderService,
    private readonly localStorage: DidLocalStorage,
    private readonly keypairManager: KeypairManagerService
  ) {}

  async onModuleInit() {
    await this.initializeDocument();
  }

  /**
   * 初始化DID文档
   * 首次启动时创建新文档，后续启动时加载现有文档
   */
  private async initializeDocument(): Promise<void> {
    try {
      this.logger.log('Initializing DID document...');

      // 尝试加载现有文档
      const existingDocument = await this.localStorage.load();
      
      if (existingDocument) {
        this.logger.log('Loading existing DID document');
        this.currentDocument = existingDocument;
        
        // 验证文档完整性
        const validation = this.versionManager.validateVersionIntegrity(existingDocument);
        if (!validation.isValid) {
          this.logger.warn(`Document validation failed: ${validation.errors.join(', ')}`);
          // 可以选择重新生成文档或修复问题
        }
      } else {
        this.logger.log('No existing document found, creating new one');
        await this.createInitialDocument();
      }

      this.logger.log(`DID document initialized: ${this.currentDocument?.id}`);
    } catch (error) {
      this.logger.error(`Failed to initialize DID document: ${error}`);
      throw error;
    }
  }

  /**
   * 创建初始DID文档
   */
  private async createInitialDocument(): Promise<void> {
    const baseState = {
      contextList: [
        'https://www.w3.org/ns/did/v1',
        'https://sight.ai/contexts/did/v1'
      ],
      seq: 1,
      hash: ''
    };

    this.currentDocument = await this.enhancedBuilder.buildEnhancedDidDocument(
      baseState,
      this.currentCapabilityConfig,
      this.currentRewardAddress
    );

    // 更新版本信息
    this.currentDocument = this.versionManager.updateVersionInfo(this.currentDocument, false);

    // 持久化文档
    await this.localStorage.persist(this.currentDocument);
  }

  /**
   * 更新奖励地址
   * @param newRewardAddress 新的奖励地址
   * @returns 是否成功更新
   */
  async updateRewardAddress(newRewardAddress: string): Promise<boolean> {
    try {
      if (!this.currentDocument) {
        throw new Error('No current document to update');
      }

      // 检查是否真的需要更新
      if (this.currentDocument.controller === newRewardAddress) {
        this.logger.debug('Reward address unchanged, skipping update');
        return false;
      }

      this.logger.log(`Updating reward address from ${this.currentDocument.controller} to ${newRewardAddress}`);

      // 保存旧文档用于变更记录
      const oldDocument = { ...this.currentDocument };

      // 生成新文档
      const updatedDocument = await this.enhancedBuilder.updateRewardAddress(
        this.currentDocument,
        newRewardAddress,
        this.currentCapabilityConfig
      );

      // 更新版本信息
      const versionedDocument = this.versionManager.updateVersionInfo(updatedDocument);

      // 创建变更记录
      const changeRecord = this.versionManager.createChangeRecord(oldDocument, versionedDocument);
      this.changeHistory.push(changeRecord);

      // 更新当前文档和状态
      this.currentDocument = versionedDocument;
      this.currentRewardAddress = newRewardAddress;

      // 持久化更新
      await this.localStorage.persist(this.currentDocument);

      this.logger.log(`Reward address updated successfully, new sequence: ${this.currentDocument['sight:seq']}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to update reward address: ${error}`);
      throw error;
    }
  }

  /**
   * 更新设备能力
   * @param newCapabilityConfig 新的能力配置
   * @returns 是否成功更新
   */
  async updateCapabilities(newCapabilityConfig: DeviceCapabilityConfig): Promise<boolean> {
    try {
      if (!this.currentDocument) {
        throw new Error('No current document to update');
      }

      // 检查是否需要更新
      if (!this.enhancedBuilder.needsUpdate(this.currentDocument, newCapabilityConfig)) {
        this.logger.debug('Capabilities unchanged, skipping update');
        return false;
      }

      this.logger.log('Updating device capabilities');

      // 保存旧文档
      const oldDocument = { ...this.currentDocument };

      // 生成新文档
      const updatedDocument = await this.enhancedBuilder.updateCapabilities(
        this.currentDocument,
        newCapabilityConfig
      );

      // 更新版本信息
      const versionedDocument = this.versionManager.updateVersionInfo(updatedDocument);

      // 创建变更记录
      const changeRecord = this.versionManager.createChangeRecord(oldDocument, versionedDocument);
      this.changeHistory.push(changeRecord);

      // 更新当前状态
      this.currentDocument = versionedDocument;
      this.currentCapabilityConfig = newCapabilityConfig;

      // 持久化更新
      await this.localStorage.persist(this.currentDocument);

      this.logger.log(`Capabilities updated successfully, new sequence: ${this.currentDocument['sight:seq']}`);
      return true;
    } catch (error) {
      this.logger.error(`Failed to update capabilities: ${error}`);
      throw error;
    }
  }

  /**
   * 强制刷新DID文档
   * 重新扫描当前配置并更新文档
   */
  async refreshDocument(): Promise<void> {
    try {
      this.logger.log('Refreshing DID document');

      if (!this.currentDocument) {
        await this.createInitialDocument();
        return;
      }

      // 检查是否需要更新
      const needsUpdate = this.enhancedBuilder.needsUpdate(
        this.currentDocument,
        this.currentCapabilityConfig,
        this.currentRewardAddress
      );

      if (needsUpdate) {
        // 保存旧文档
        const oldDocument = { ...this.currentDocument };

        // 重新构建文档
        const baseState = {
          contextList: Array.isArray(this.currentDocument['@context']) 
            ? this.currentDocument['@context'] as string[]
            : [this.currentDocument['@context'] as string],
          seq: this.versionManager.extractSequence(this.currentDocument),
          hash: this.currentDocument['sight:hash'] || ''
        };

        const refreshedDocument = await this.enhancedBuilder.buildEnhancedDidDocument(
          baseState,
          this.currentCapabilityConfig,
          this.currentRewardAddress
        );

        // 更新版本信息
        const versionedDocument = this.versionManager.updateVersionInfo(refreshedDocument);

        // 创建变更记录
        const changeRecord = this.versionManager.createChangeRecord(oldDocument, versionedDocument);
        this.changeHistory.push(changeRecord);

        // 更新当前文档
        this.currentDocument = versionedDocument;

        // 持久化更新
        await this.localStorage.persist(this.currentDocument);

        this.logger.log('Document refreshed successfully');
      } else {
        this.logger.debug('No refresh needed');
      }
    } catch (error) {
      this.logger.error(`Failed to refresh document: ${error}`);
      throw error;
    }
  }

  /**
   * 获取当前DID文档
   */
  getCurrentDocument(): RawDidDocument | null {
    return this.currentDocument;
  }

  /**
   * 获取文档变更历史
   */
  getChangeHistory(): DidDocumentChangeRecord[] {
    return [...this.changeHistory];
  }

  /**
   * 获取文档状态信息
   */
  getDocumentStatus(): DidDocumentStatus {
    if (!this.currentDocument) {
      return {
        exists: false,
        isValid: false,
        sequence: 0,
        lastUpdated: null,
        rewardAddress: undefined,
        hasCapabilities: false
      };
    }

    const validation = this.versionManager.validateVersionIntegrity(this.currentDocument);
    
    return {
      exists: true,
      isValid: validation.isValid,
      sequence: this.versionManager.extractSequence(this.currentDocument),
      lastUpdated: this.currentDocument['sight:updatedAt'] || this.currentDocument['sight:createdAt'] || null,
      rewardAddress: this.currentDocument.controller,
      hasCapabilities: (this.currentDocument.service || []).length > 0,
      validationErrors: validation.errors,
      validationWarnings: validation.warnings
    };
  }

  /**
   * 设置当前配置
   */
  setCurrentConfig(rewardAddress?: string, capabilityConfig?: DeviceCapabilityConfig): void {
    this.currentRewardAddress = rewardAddress;
    this.currentCapabilityConfig = capabilityConfig;
  }

  /**
   * 检查是否需要更新
   */
  needsUpdate(rewardAddress?: string, capabilityConfig?: DeviceCapabilityConfig): boolean {
    if (!this.currentDocument) {
      return true;
    }

    return this.enhancedBuilder.needsUpdate(this.currentDocument, capabilityConfig, rewardAddress);
  }
}

/**
 * DID文档状态信息
 */
export interface DidDocumentStatus {
  exists: boolean;
  isValid: boolean;
  sequence: number;
  lastUpdated: string | null;
  rewardAddress: string | undefined;
  hasCapabilities: boolean;
  validationErrors?: string[];
  validationWarnings?: string[];
}
