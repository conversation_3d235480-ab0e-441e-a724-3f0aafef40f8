import nacl from 'tweetnacl';

/**
 * 密钥对存储接口
 */
export interface KeypairStorageInterface {
  /**
   * 生成新的密钥对
   * @returns 生成的密钥对
   */
  generateKeyPair(): nacl.SignKeyPair;

  /**
   * 保存密钥对到本地存储
   * @param keyPair 要保存的密钥对
   * @param filePath 可选的文件路径，默认使用标准路径
   */
  saveKeyPair(keyPair: nacl.SignKeyPair, filePath?: string): Promise<void>;

  /**
   * 从本地存储加载密钥对
   * @param filePath 可选的文件路径，默认使用标准路径
   * @returns 加载的密钥对，如果不存在则返回null
   */
  loadKeyPair(filePath?: string): Promise<nacl.SignKeyPair | null>;

  /**
   * 检查密钥对文件是否存在
   * @param filePath 可选的文件路径，默认使用标准路径
   * @returns 文件是否存在
   */
  keyPairExists(filePath?: string): Promise<boolean>;

  /**
   * 获取默认的密钥对存储路径
   * @returns 默认存储路径
   */
  getDefaultKeyPairPath(): string;

  /**
   * 删除密钥对文件
   * @param filePath 可选的文件路径，默认使用标准路径
   */
  deleteKeyPair(filePath?: string): Promise<void>;

  /**
   * 设置文件权限为仅当前用户可访问 (chmod 600)
   * @param filePath 文件路径
   */
  setSecurePermissions(filePath: string): Promise<void>;
}

/**
 * 密钥对存储配置
 */
export interface KeypairStorageConfig {
  /** 存储目录路径，默认为 ~/.sightai */
  storageDir?: string;
  /** 密钥对文件名，默认为 keypair.json */
  fileName?: string;
  /** 是否自动创建目录 */
  autoCreateDir?: boolean;
  /** 文件权限模式，默认为 0o600 */
  fileMode?: number;
}

/**
 * 存储的密钥对数据结构
 */
export interface StoredKeyPair {
  /** 公钥 (Base64编码) */
  publicKey: string;
  /** 私钥 (Base64编码) */
  secretKey: string;
  /** 创建时间戳 */
  createdAt: number;
  /** DID标识符 */
  did?: string;
  /** 版本号 */
  version: string;
}
