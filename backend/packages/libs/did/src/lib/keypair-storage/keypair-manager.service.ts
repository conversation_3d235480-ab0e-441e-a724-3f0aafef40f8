import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import nacl from 'tweetnacl';
import { KeypairStorageService } from './keypair-storage.service';
import { toPeerId, toPublicKeyBase58 } from '../did.utils';

/**
 * 密钥对管理器服务
 * 负责管理设备的密钥对生命周期
 */
@Injectable()
export class KeypairManagerService implements OnModuleInit {
  private readonly logger = new Logger(KeypairManagerService.name);
  private keyPair: nacl.SignKeyPair | null = null;
  private did: string | null = null;
  private peerId: string | null = null;
  private publicKeyBase58: string | null = null;

  constructor(
    private readonly keypairStorage: KeypairStorageService
  ) {}

  async onModuleInit() {
    await this.initializeKeyPair();
  }

  /**
   * 初始化密钥对
   * 首次启动时生成新密钥对，后续启动时加载现有密钥对
   */
  private async initializeKeyPair(): Promise<void> {
    try {
      this.logger.log('Initializing key pair...');
      
      // 尝试加载现有密钥对
      this.keyPair = await this.keypairStorage.loadKeyPair();
      
      if (!this.keyPair) {
        this.logger.log('No existing key pair found, generating new one...');
        this.keyPair = this.keypairStorage.generateKeyPair();
        await this.keypairStorage.saveKeyPair(this.keyPair);
        this.logger.log('New key pair generated and saved');
      } else {
        this.logger.log('Existing key pair loaded successfully');
      }

      // 计算派生信息
      this.publicKeyBase58 = toPublicKeyBase58(this.keyPair);
      this.peerId = toPeerId(this.publicKeyBase58);
      this.did = `did:sight:hoster:${this.peerId}`;

      this.logger.log(`Initialized with DID: ${this.did}`);
      this.logger.log(`Peer ID: ${this.peerId}`);
    } catch (error) {
      this.logger.error(`Failed to initialize key pair: ${error}`);
      throw error;
    }
  }

  /**
   * 获取当前密钥对
   */
  getKeyPair(): nacl.SignKeyPair {
    if (!this.keyPair) {
      throw new Error('Key pair not initialized');
    }
    return this.keyPair;
  }

  /**
   * 获取DID标识符
   */
  getDid(): string {
    if (!this.did) {
      throw new Error('DID not initialized');
    }
    return this.did;
  }

  /**
   * 获取Peer ID
   */
  getPeerId(): string {
    if (!this.peerId) {
      throw new Error('Peer ID not initialized');
    }
    return this.peerId;
  }

  /**
   * 获取Base58编码的公钥
   */
  getPublicKeyBase58(): string {
    if (!this.publicKeyBase58) {
      throw new Error('Public key not initialized');
    }
    return this.publicKeyBase58;
  }

  /**
   * 获取公钥的Uint8Array格式
   */
  getPublicKey(): Uint8Array {
    return this.getKeyPair().publicKey;
  }

  /**
   * 获取私钥的Uint8Array格式
   */
  getSecretKey(): Uint8Array {
    return this.getKeyPair().secretKey;
  }

  /**
   * 签名数据
   * @param data 要签名的数据
   * @returns Base58编码的签名
   */
  signData(data: Uint8Array): string {
    const signature = nacl.sign.detached(data, this.getSecretKey());
    return Buffer.from(signature).toString('base64');
  }

  /**
   * 签名JSON对象
   * @param obj 要签名的对象
   * @returns Base58编码的签名
   */
  signObject(obj: any): string {
    const jsonString = JSON.stringify(obj);
    const data = new TextEncoder().encode(jsonString);
    return this.signData(data);
  }

  /**
   * 验证签名
   * @param data 原始数据
   * @param signature Base64编码的签名
   * @param publicKey 公钥（可选，默认使用当前密钥对的公钥）
   * @returns 验证结果
   */
  verifySignature(data: Uint8Array, signature: string, publicKey?: Uint8Array): boolean {
    try {
      const signatureBytes = Buffer.from(signature, 'base64');
      const pubKey = publicKey || this.getPublicKey();
      return nacl.sign.detached.verify(data, signatureBytes, pubKey);
    } catch (error) {
      this.logger.error(`Signature verification failed: ${error}`);
      return false;
    }
  }

  /**
   * 验证JSON对象的签名
   * @param obj 原始对象
   * @param signature Base64编码的签名
   * @param publicKey 公钥（可选）
   * @returns 验证结果
   */
  verifyObjectSignature(obj: any, signature: string, publicKey?: Uint8Array): boolean {
    const jsonString = JSON.stringify(obj);
    const data = new TextEncoder().encode(jsonString);
    return this.verifySignature(data, signature, publicKey);
  }

  /**
   * 重新生成密钥对
   * 注意：这将使所有基于旧密钥对的DID和签名失效
   */
  async regenerateKeyPair(): Promise<void> {
    this.logger.warn('Regenerating key pair - this will invalidate existing DID and signatures');
    
    // 删除旧的密钥对文件
    await this.keypairStorage.deleteKeyPair();
    
    // 重新初始化
    await this.initializeKeyPair();
    
    this.logger.log('Key pair regenerated successfully');
  }

  /**
   * 检查密钥对是否已初始化
   */
  isInitialized(): boolean {
    return this.keyPair !== null && this.did !== null && this.peerId !== null;
  }

  /**
   * 获取密钥对信息摘要
   */
  getKeyPairInfo(): {
    did: string;
    peerId: string;
    publicKeyBase58: string;
    hasSecretKey: boolean;
  } {
    return {
      did: this.getDid(),
      peerId: this.getPeerId(),
      publicKeyBase58: this.getPublicKeyBase58(),
      hasSecretKey: !!this.keyPair?.secretKey
    };
  }
}
