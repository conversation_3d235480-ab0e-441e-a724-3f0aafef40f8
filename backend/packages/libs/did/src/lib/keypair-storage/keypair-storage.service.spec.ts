import { Test, TestingModule } from '@nestjs/testing';
import { promises as fs } from 'fs';
import * as path from 'path';
import * as os from 'os';
import { KeypairStorageService } from './keypair-storage.service';
import { KeypairManagerService } from './keypair-manager.service';

describe('KeypairStorageService', () => {
  let service: KeypairStorageService;
  let testDir: string;

  beforeEach(async () => {
    // 创建临时测试目录
    testDir = path.join(os.tmpdir(), 'sightai-test-' + Date.now());
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: KeypairStorageService,
          useFactory: () => new KeypairStorageService({
            storageDir: testDir,
            fileName: 'test-keypair.json'
          })
        }
      ],
    }).compile();

    service = module.get<KeypairStorageService>(KeypairStorageService);
  });

  afterEach(async () => {
    // 清理测试目录
    try {
      await fs.rmdir(testDir, { recursive: true });
    } catch (error) {
      // 忽略清理错误
    }
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should generate a key pair', () => {
    const keyPair = service.generateKeyPair();
    expect(keyPair).toBeDefined();
    expect(keyPair.publicKey).toBeInstanceOf(Uint8Array);
    expect(keyPair.secretKey).toBeInstanceOf(Uint8Array);
    expect(keyPair.publicKey.length).toBe(32);
    expect(keyPair.secretKey.length).toBe(64);
  });

  it('should save and load a key pair', async () => {
    const originalKeyPair = service.generateKeyPair();
    
    // 保存密钥对
    await service.saveKeyPair(originalKeyPair);
    
    // 检查文件是否存在
    expect(await service.keyPairExists()).toBe(true);
    
    // 加载密钥对
    const loadedKeyPair = await service.loadKeyPair();
    
    expect(loadedKeyPair).toBeDefined();
    expect(loadedKeyPair!.publicKey).toEqual(originalKeyPair.publicKey);
    expect(loadedKeyPair!.secretKey).toEqual(originalKeyPair.secretKey);
  });

  it('should return null when loading non-existent key pair', async () => {
    const keyPair = await service.loadKeyPair();
    expect(keyPair).toBeNull();
  });

  it('should delete key pair', async () => {
    const keyPair = service.generateKeyPair();
    await service.saveKeyPair(keyPair);
    
    expect(await service.keyPairExists()).toBe(true);
    
    await service.deleteKeyPair();
    
    expect(await service.keyPairExists()).toBe(false);
  });

  it('should get or create key pair', async () => {
    // 第一次调用应该创建新的密钥对
    const keyPair1 = await service.getOrCreateKeyPair();
    expect(keyPair1).toBeDefined();
    expect(await service.keyPairExists()).toBe(true);
    
    // 第二次调用应该加载现有的密钥对
    const keyPair2 = await service.getOrCreateKeyPair();
    expect(keyPair2).toBeDefined();
    expect(keyPair2.publicKey).toEqual(keyPair1.publicKey);
    expect(keyPair2.secretKey).toEqual(keyPair1.secretKey);
  });
});

describe('KeypairManagerService', () => {
  let service: KeypairManagerService;
  let storageService: KeypairStorageService;
  let testDir: string;

  beforeEach(async () => {
    testDir = path.join(os.tmpdir(), 'sightai-manager-test-' + Date.now());
    
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: KeypairStorageService,
          useFactory: () => new KeypairStorageService({
            storageDir: testDir,
            fileName: 'test-keypair.json'
          })
        },
        KeypairManagerService
      ],
    }).compile();

    service = module.get<KeypairManagerService>(KeypairManagerService);
    storageService = module.get<KeypairStorageService>(KeypairStorageService);
    
    // 手动初始化，因为在测试中OnModuleInit不会自动调用
    await service.onModuleInit();
  });

  afterEach(async () => {
    try {
      await fs.rmdir(testDir, { recursive: true });
    } catch (error) {
      // 忽略清理错误
    }
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should initialize with key pair', () => {
    expect(service.isInitialized()).toBe(true);
    expect(service.getDid()).toMatch(/^did:sight:hoster:/);
    expect(service.getPeerId()).toBeDefined();
    expect(service.getPublicKeyBase58()).toBeDefined();
  });

  it('should sign and verify data', () => {
    const testData = new TextEncoder().encode('test message');
    const signature = service.signData(testData);
    
    expect(signature).toBeDefined();
    expect(typeof signature).toBe('string');
    
    const isValid = service.verifySignature(testData, signature);
    expect(isValid).toBe(true);
    
    // 测试错误的签名
    const wrongSignature = 'wrong-signature';
    const isInvalid = service.verifySignature(testData, wrongSignature);
    expect(isInvalid).toBe(false);
  });

  it('should sign and verify objects', () => {
    const testObject = { message: 'test', timestamp: Date.now() };
    const signature = service.signObject(testObject);
    
    expect(signature).toBeDefined();
    
    const isValid = service.verifyObjectSignature(testObject, signature);
    expect(isValid).toBe(true);
    
    // 测试修改后的对象
    const modifiedObject = { ...testObject, message: 'modified' };
    const isInvalid = service.verifyObjectSignature(modifiedObject, signature);
    expect(isInvalid).toBe(false);
  });

  it('should provide key pair info', () => {
    const info = service.getKeyPairInfo();
    
    expect(info.did).toMatch(/^did:sight:hoster:/);
    expect(info.peerId).toBeDefined();
    expect(info.publicKeyBase58).toBeDefined();
    expect(info.hasSecretKey).toBe(true);
  });
});
