import { Injectable, Logger } from '@nestjs/common';
import { promises as fs } from 'fs';
import * as path from 'path';
import * as os from 'os';
import nacl from 'tweetnacl';
import { 
  KeypairStorageInterface, 
  KeypairStorageConfig, 
  StoredKeyPair 
} from './keypair-storage.interface';

/**
 * 密钥对本地存储服务实现
 */
@Injectable()
export class KeypairStorageService implements KeypairStorageInterface {
  private readonly logger = new Logger(KeypairStorageService.name);
  private readonly config: Required<KeypairStorageConfig>;

  constructor(config?: KeypairStorageConfig) {
    this.config = {
      storageDir: config?.storageDir || path.join(os.homedir(), '.sightai'),
      fileName: config?.fileName || 'keypair.json',
      autoCreateDir: config?.autoCreateDir ?? true,
      fileMode: config?.fileMode ?? 0o600
    };
  }

  /**
   * 生成新的密钥对
   */
  generateKeyPair(): nacl.SignKeyPair {
    this.logger.log('Generating new Ed25519 key pair');
    return nacl.sign.keyPair();
  }

  /**
   * 保存密钥对到本地存储
   */
  async saveKeyPair(keyPair: nacl.SignKeyPair, filePath?: string): Promise<void> {
    const targetPath = filePath || this.getDefaultKeyPairPath();
    
    try {
      // 确保目录存在
      if (this.config.autoCreateDir) {
        await this.ensureDirectoryExists(path.dirname(targetPath));
      }

      // 准备存储数据
      const storedKeyPair: StoredKeyPair = {
        publicKey: Buffer.from(keyPair.publicKey).toString('base64'),
        secretKey: Buffer.from(keyPair.secretKey).toString('base64'),
        createdAt: Date.now(),
        version: '1.0.0'
      };

      // 写入文件
      await fs.writeFile(targetPath, JSON.stringify(storedKeyPair, null, 2), 'utf8');
      
      // 设置安全权限
      await this.setSecurePermissions(targetPath);
      
      this.logger.log(`Key pair saved to: ${targetPath}`);
    } catch (error) {
      this.logger.error(`Failed to save key pair: ${error}`);
      throw new Error(`Failed to save key pair: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 从本地存储加载密钥对
   */
  async loadKeyPair(filePath?: string): Promise<nacl.SignKeyPair | null> {
    const targetPath = filePath || this.getDefaultKeyPairPath();
    
    try {
      // 检查文件是否存在
      if (!(await this.keyPairExists(targetPath))) {
        this.logger.debug(`Key pair file not found: ${targetPath}`);
        return null;
      }

      // 读取文件
      const fileContent = await fs.readFile(targetPath, 'utf8');
      const storedKeyPair: StoredKeyPair = JSON.parse(fileContent);

      // 验证数据结构
      if (!storedKeyPair.publicKey || !storedKeyPair.secretKey) {
        throw new Error('Invalid key pair file format');
      }

      // 转换回密钥对格式
      const keyPair: nacl.SignKeyPair = {
        publicKey: new Uint8Array(Buffer.from(storedKeyPair.publicKey, 'base64')),
        secretKey: new Uint8Array(Buffer.from(storedKeyPair.secretKey, 'base64'))
      };

      this.logger.log(`Key pair loaded from: ${targetPath}`);
      return keyPair;
    } catch (error) {
      this.logger.error(`Failed to load key pair: ${error}`);
      throw new Error(`Failed to load key pair: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 检查密钥对文件是否存在
   */
  async keyPairExists(filePath?: string): Promise<boolean> {
    const targetPath = filePath || this.getDefaultKeyPairPath();
    
    try {
      await fs.access(targetPath, fs.constants.F_OK);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * 获取默认的密钥对存储路径
   */
  getDefaultKeyPairPath(): string {
    return path.join(this.config.storageDir, this.config.fileName);
  }

  /**
   * 删除密钥对文件
   */
  async deleteKeyPair(filePath?: string): Promise<void> {
    const targetPath = filePath || this.getDefaultKeyPairPath();
    
    try {
      if (await this.keyPairExists(targetPath)) {
        await fs.unlink(targetPath);
        this.logger.log(`Key pair deleted: ${targetPath}`);
      }
    } catch (error) {
      this.logger.error(`Failed to delete key pair: ${error}`);
      throw new Error(`Failed to delete key pair: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 设置文件权限为仅当前用户可访问 (chmod 600)
   */
  async setSecurePermissions(filePath: string): Promise<void> {
    try {
      await fs.chmod(filePath, this.config.fileMode);
      this.logger.debug(`Set secure permissions (${this.config.fileMode.toString(8)}) for: ${filePath}`);
    } catch (error) {
      this.logger.warn(`Failed to set secure permissions: ${error}`);
      // 不抛出错误，因为在某些系统上可能不支持chmod
    }
  }

  /**
   * 确保目录存在
   */
  private async ensureDirectoryExists(dirPath: string): Promise<void> {
    try {
      await fs.mkdir(dirPath, { recursive: true, mode: 0o700 });
      this.logger.debug(`Directory ensured: ${dirPath}`);
    } catch (error) {
      this.logger.error(`Failed to create directory: ${error}`);
      throw new Error(`Failed to create directory: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 获取或创建密钥对
   * 如果本地存在密钥对则加载，否则生成新的密钥对并保存
   */
  async getOrCreateKeyPair(filePath?: string): Promise<nacl.SignKeyPair> {
    const existingKeyPair = await this.loadKeyPair(filePath);
    
    if (existingKeyPair) {
      this.logger.log('Using existing key pair');
      return existingKeyPair;
    }

    this.logger.log('Generating and saving new key pair');
    const newKeyPair = this.generateKeyPair();
    await this.saveKeyPair(newKeyPair, filePath);
    return newKeyPair;
  }
}
