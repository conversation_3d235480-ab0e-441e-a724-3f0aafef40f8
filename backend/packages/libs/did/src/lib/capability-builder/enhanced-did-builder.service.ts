import { Injectable, Logger } from '@nestjs/common';
import { RawDidDocument, DidLocalState } from '@saito/models';
import { DidLocalBuilder } from '../did-local.builder';
import { DeviceCapabilityBuilder, DeviceCapabilityConfig } from './device-capability.builder';
import { KeypairManagerService } from '../keypair-storage';

/**
 * 增强的DID文档构建器
 * 集成设备能力声明和密钥管理
 */
@Injectable()
export class EnhancedDidBuilderService {
  private readonly logger = new Logger(EnhancedDidBuilderService.name);

  constructor(
    private readonly didLocalBuilder: DidLocalBuilder,
    private readonly capabilityBuilder: DeviceCapabilityBuilder,
    private readonly keypairManager: KeypairManagerService
  ) {}

  /**
   * 构建包含设备能力的DID文档
   * @param baseState 基础DID状态
   * @param capabilityConfig 设备能力配置
   * @param rewardAddress 奖励地址
   * @returns 完整的DID文档
   */
  async buildEnhancedDidDocument(
    baseState: Partial<DidLocalState>,
    capabilityConfig?: DeviceCapabilityConfig,
    rewardAddress?: string
  ): Promise<RawDidDocument> {
    try {
      // 确保密钥管理器已初始化
      if (!this.keypairManager.isInitialized()) {
        throw new Error('Keypair manager not initialized');
      }

      // 获取基础信息
      const keyPair = this.keypairManager.getKeyPair();
      const did = this.keypairManager.getDid();
      const publicKeyBase58 = this.keypairManager.getPublicKeyBase58();

      // 构建设备能力服务列表
      const serviceList = this.capabilityBuilder.buildServiceList(did, capabilityConfig);

      // 构建验证方法
      const verificationMethod = [
        {
          id: `${did}#key-1`,
          type: 'Ed25519VerificationKey2020',
          controller: did,
          publicKeyMultibase: publicKeyBase58,
        }
      ];

      // 构建完整的DID状态
      const enhancedState: DidLocalState = {
        contextList: baseState.contextList || [
          'https://www.w3.org/ns/did/v1',
          'https://sight.ai/contexts/did/v1'
        ],
        serviceList,
        keyPair,
        publicKey: publicKeyBase58,
        did,
        authentication: `${did}#key-1`,
        verificationMethod,
        seq: baseState.seq || 1,
        hash: baseState.hash || this.generateHash(),
        controller: rewardAddress // 使用奖励地址作为controller
      };

      // 使用原有的构建器生成DID文档
      const didDocument = await this.didLocalBuilder.build(enhancedState);

      // 添加自定义字段
      const enhancedDocument = {
        ...didDocument,
        'sight:deviceType': capabilityConfig?.aiCapabilities ? 'ai-node' : 'relay-node',
        'sight:capabilities': this.extractCapabilityTags(serviceList),
        'sight:version': '1.0.0',
        'sight:createdAt': new Date().toISOString()
      };

      this.logger.log(`Built enhanced DID document for ${did} with ${serviceList.length} services`);
      return enhancedDocument;
    } catch (error) {
      this.logger.error(`Failed to build enhanced DID document: ${error}`);
      throw error;
    }
  }

  /**
   * 更新DID文档的奖励地址
   * @param currentDocument 当前DID文档
   * @param newRewardAddress 新的奖励地址
   * @param capabilityConfig 设备能力配置
   * @returns 更新后的DID文档
   */
  async updateRewardAddress(
    currentDocument: RawDidDocument,
    newRewardAddress: string,
    capabilityConfig?: DeviceCapabilityConfig
  ): Promise<RawDidDocument> {
    try {
      // 提取当前状态
      const currentSeq = currentDocument['sight:seq'] || 1;
      const baseState: Partial<DidLocalState> = {
        contextList: Array.isArray(currentDocument['@context']) 
          ? currentDocument['@context'] as string[]
          : [currentDocument['@context'] as string],
        seq: typeof currentSeq === 'number' ? currentSeq + 1 : parseInt(String(currentSeq)) + 1,
        hash: this.generateHash()
      };

      // 构建新的DID文档
      const updatedDocument = await this.buildEnhancedDidDocument(
        baseState,
        capabilityConfig,
        newRewardAddress
      );

      this.logger.log(`Updated DID document with new reward address: ${newRewardAddress}`);
      return updatedDocument;
    } catch (error) {
      this.logger.error(`Failed to update reward address: ${error}`);
      throw error;
    }
  }

  /**
   * 更新设备能力
   * @param currentDocument 当前DID文档
   * @param newCapabilityConfig 新的能力配置
   * @returns 更新后的DID文档
   */
  async updateCapabilities(
    currentDocument: RawDidDocument,
    newCapabilityConfig: DeviceCapabilityConfig
  ): Promise<RawDidDocument> {
    try {
      const currentSeq = currentDocument['sight:seq'] || 1;
      const currentController = currentDocument.controller;
      
      const baseState: Partial<DidLocalState> = {
        contextList: Array.isArray(currentDocument['@context']) 
          ? currentDocument['@context'] as string[]
          : [currentDocument['@context'] as string],
        seq: typeof currentSeq === 'number' ? currentSeq + 1 : parseInt(String(currentSeq)) + 1,
        hash: this.generateHash()
      };

      const updatedDocument = await this.buildEnhancedDidDocument(
        baseState,
        newCapabilityConfig,
        currentController
      );

      this.logger.log(`Updated DID document with new capabilities`);
      return updatedDocument;
    } catch (error) {
      this.logger.error(`Failed to update capabilities: ${error}`);
      throw error;
    }
  }

  /**
   * 检查DID文档是否需要更新
   * @param currentDocument 当前DID文档
   * @param newCapabilityConfig 新的能力配置
   * @param newRewardAddress 新的奖励地址
   * @returns 是否需要更新
   */
  needsUpdate(
    currentDocument: RawDidDocument,
    newCapabilityConfig?: DeviceCapabilityConfig,
    newRewardAddress?: string
  ): boolean {
    // 检查奖励地址是否变更
    if (newRewardAddress && currentDocument.controller !== newRewardAddress) {
      this.logger.debug('Reward address changed, update needed');
      return true;
    }

    // 检查能力是否变更
    if (newCapabilityConfig) {
      const currentServices = currentDocument.service || [];
      const newServices = this.capabilityBuilder.buildServiceList(
        currentDocument.id,
        newCapabilityConfig
      );

      if (this.servicesChanged(currentServices, newServices)) {
        this.logger.debug('Device capabilities changed, update needed');
        return true;
      }
    }

    return false;
  }

  /**
   * 提取能力标签
   */
  private extractCapabilityTags(serviceList: any[]): string[] {
    const tags = new Set<string>();
    
    for (const service of serviceList) {
      switch (service.type) {
        case 'TunnelCommunication':
          tags.add('communication');
          break;
        case 'AIChatService':
          tags.add('ai-chat');
          break;
        case 'AIInferenceService':
          tags.add('ai-inference');
          break;
        case 'P2PRoutingService':
          tags.add('p2p-routing');
          break;
        case 'NodeDiscoveryService':
          tags.add('node-discovery');
          break;
        case 'DistributedStorageService':
          tags.add('distributed-storage');
          break;
      }
    }

    return Array.from(tags);
  }

  /**
   * 生成文档哈希
   */
  private generateHash(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  /**
   * 检查服务列表是否发生变更
   */
  private servicesChanged(currentServices: any[], newServices: any[]): boolean {
    if (currentServices.length !== newServices.length) {
      return true;
    }

    // 简单的服务类型比较
    const currentTypes = currentServices.map(s => s.type).sort();
    const newTypes = newServices.map(s => s.type).sort();
    
    return JSON.stringify(currentTypes) !== JSON.stringify(newTypes);
  }

  /**
   * 验证DID文档的完整性
   * @param document DID文档
   * @returns 验证结果
   */
  validateDocument(document: RawDidDocument): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];

    // 基础字段验证
    if (!document.id) {
      errors.push('Missing DID identifier');
    }

    if (!document.verificationMethod || document.verificationMethod.length === 0) {
      errors.push('Missing verification methods');
    }

    if (!document.authentication || document.authentication.length === 0) {
      errors.push('Missing authentication methods');
    }

    if (!document.service || document.service.length === 0) {
      errors.push('Missing service declarations');
    }

    if (!document.proof) {
      errors.push('Missing cryptographic proof');
    }

    // 自定义字段验证
    if (!document['sight:seq']) {
      errors.push('Missing sequence number');
    }

    if (!document['sight:hash']) {
      errors.push('Missing document hash');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }
}
