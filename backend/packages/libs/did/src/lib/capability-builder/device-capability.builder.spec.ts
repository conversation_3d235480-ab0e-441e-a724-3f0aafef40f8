import { Test, TestingModule } from '@nestjs/testing';
import { DeviceCapabilityBuilder, DeviceCapabilityConfig } from './device-capability.builder';
import { MessageHandlerRegistry } from '@saito/tunnel';

describe('DeviceCapabilityBuilder', () => {
  let builder: DeviceCapabilityBuilder;
  let mockMessageHandlerRegistry: jest.Mocked<MessageHandlerRegistry>;

  beforeEach(async () => {
    // 创建模拟的MessageHandlerRegistry
    mockMessageHandlerRegistry = {
      getHandlerDescriptors: jest.fn().mockReturnValue([
        {
          direction: 'income',
          type: 'chat_request_stream',
          className: 'IncomeChatRequestStreamHandler'
        },
        {
          direction: 'outcome',
          type: 'chat_request_stream',
          className: 'OutcomeChatRequestStreamHandler'
        },
        {
          direction: 'income',
          type: 'completion_request_stream',
          className: 'IncomeCompletionRequestStreamHandler'
        },
        {
          direction: 'income',
          type: 'ping',
          className: 'IncomePingHandler'
        },
        {
          direction: 'outcome',
          type: 'pong',
          className: 'OutcomePongHandler'
        }
      ])
    } as any;

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        DeviceCapabilityBuilder,
        {
          provide: MessageHandlerRegistry,
          useValue: mockMessageHandlerRegistry
        }
      ],
    }).compile();

    builder = module.get<DeviceCapabilityBuilder>(DeviceCapabilityBuilder);
  });

  it('should be defined', () => {
    expect(builder).toBeDefined();
  });

  describe('buildServiceList', () => {
    it('should build basic communication services', () => {
      const did = 'did:sight:hoster:test123';
      const services = builder.buildServiceList(did);

      expect(services).toBeDefined();
      expect(services.length).toBeGreaterThan(0);

      // 应该包含基础通信服务
      const commService = services.find(s => s.type === 'TunnelCommunication');
      expect(commService).toBeDefined();
      expect(commService?.id).toBe(`${did}#tunnel-communication`);
      expect(commService?.serviceEndpoint).toBe('tunnel://communication');
    });

    it('should build AI chat service when chat handlers exist', () => {
      const did = 'did:sight:hoster:test123';
      const services = builder.buildServiceList(did);

      const chatService = services.find(s => s.type === 'AIChatService');
      expect(chatService).toBeDefined();
      expect(chatService?.id).toBe(`${did}#ai-chat`);
      expect(chatService?.capabilities?.streaming).toBe(true);
    });

    it('should build P2P network services', () => {
      const did = 'did:sight:hoster:test123';
      const services = builder.buildServiceList(did);

      const routingService = services.find(s => s.type === 'P2PRoutingService');
      expect(routingService).toBeDefined();
      expect(routingService?.id).toBe(`${did}#p2p-routing`);

      const discoveryService = services.find(s => s.type === 'NodeDiscoveryService');
      expect(discoveryService).toBeDefined();
      expect(discoveryService?.id).toBe(`${did}#node-discovery`);
    });

    it('should include AI inference service when configured', () => {
      const did = 'did:sight:hoster:test123';
      const config: DeviceCapabilityConfig = {
        aiCapabilities: {
          supportedModels: ['llama2', 'gpt-3.5'],
          maxContextLength: 8192,
          gpuAccelerated: true,
          streaming: true
        }
      };

      const services = builder.buildServiceList(did, config);
      const inferenceService = services.find(s => s.type === 'AIInferenceService');
      
      expect(inferenceService).toBeDefined();
      expect(inferenceService?.capabilities?.models).toEqual(['llama2', 'gpt-3.5']);
      expect(inferenceService?.capabilities?.maxContextLength).toBe(8192);
      expect(inferenceService?.capabilities?.gpuAccelerated).toBe(true);
    });

    it('should include storage service when configured', () => {
      const did = 'did:sight:hoster:test123';
      const config: DeviceCapabilityConfig = {
        storageCapabilities: {
          capacity: 1000000000, // 1GB
          replication: 3,
          encryption: true,
          supportedTypes: ['file', 'object']
        }
      };

      const services = builder.buildServiceList(did, config);
      const storageService = services.find(s => s.type === 'DistributedStorageService');
      
      expect(storageService).toBeDefined();
      expect(storageService?.capabilities?.capacity).toBe(1000000000);
      expect(storageService?.capabilities?.replication).toBe(3);
      expect(storageService?.capabilities?.supportedTypes).toEqual(['file', 'object']);
    });

    it('should include custom services when provided', () => {
      const did = 'did:sight:hoster:test123';
      const customService = {
        id: `${did}#custom-service`,
        type: 'CustomService',
        serviceEndpoint: 'custom://endpoint',
        description: 'Custom service for testing'
      };

      const config: DeviceCapabilityConfig = {
        customServices: [customService]
      };

      const services = builder.buildServiceList(did, config);
      const foundCustomService = services.find(s => s.type === 'CustomService');
      
      expect(foundCustomService).toBeDefined();
      expect(foundCustomService).toEqual(customService);
    });
  });

  describe('getCapabilitySummary', () => {
    it('should generate correct capability summary', () => {
      const did = 'did:sight:hoster:test123';
      const config: DeviceCapabilityConfig = {
        aiCapabilities: {
          supportedModels: ['llama2'],
          supportedFormats: ['text']
        },
        storageCapabilities: {
          capacity: *********
        }
      };

      const services = builder.buildServiceList(did, config);
      const summary = builder.getCapabilitySummary(services);

      expect(summary.totalServices).toBe(services.length);
      expect(summary.aiCapabilities.hasInference).toBe(true);
      expect(summary.networkCapabilities.hasRouting).toBe(true);
      expect(summary.networkCapabilities.hasDiscovery).toBe(true);
      expect(summary.storageCapabilities.hasStorage).toBe(true);
      expect(summary.storageCapabilities.capacity).toBe(*********);
    });

    it('should handle empty service list', () => {
      const summary = builder.getCapabilitySummary([]);

      expect(summary.totalServices).toBe(0);
      expect(summary.serviceTypes).toEqual([]);
      expect(summary.aiCapabilities.hasInference).toBe(false);
      expect(summary.networkCapabilities.hasRouting).toBe(false);
      expect(summary.storageCapabilities.hasStorage).toBe(false);
    });
  });
});
