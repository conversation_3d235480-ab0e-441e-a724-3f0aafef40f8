import { Injectable, Logger } from '@nestjs/common';
import { MessageHandlerRegistry } from '@saito/tunnel';
import { DidDocumentService } from '@saito/models';

/**
 * 设备能力构建器
 * 基于MessageHandler注册信息和设备配置构建DID文档的service字段
 */
@Injectable()
export class DeviceCapabilityBuilder {
  private readonly logger = new Logger(DeviceCapabilityBuilder.name);

  constructor(
    private readonly messageHandlerRegistry: MessageHandlerRegistry
  ) {}

  /**
   * 构建设备能力的service列表
   * @param did 设备DID
   * @param additionalCapabilities 额外的能力配置
   * @returns DID文档的service数组
   */
  buildServiceList(did: string, additionalCapabilities?: DeviceCapabilityConfig): DidDocumentService[] {
    const services: DidDocumentService[] = [];

    // 基于MessageHandler构建基础通信能力
    const communicationServices = this.buildCommunicationServices(did);
    services.push(...communicationServices);

    // 添加AI推理能力
    const inferenceServices = this.buildInferenceServices(did, additionalCapabilities);
    services.push(...inferenceServices);

    // 添加P2P网络能力
    const networkServices = this.buildNetworkServices(did, additionalCapabilities);
    services.push(...networkServices);

    // 添加存储能力
    const storageServices = this.buildStorageServices(did, additionalCapabilities);
    services.push(...storageServices);

    // 添加自定义服务
    if (additionalCapabilities?.customServices) {
      services.push(...additionalCapabilities.customServices);
    }

    this.logger.debug(`Built ${services.length} services for device ${did}`);
    return services;
  }

  /**
   * 基于MessageHandler构建通信服务
   */
  private buildCommunicationServices(did: string): DidDocumentService[] {
    const services: DidDocumentService[] = [];
    const handlerDescriptors = this.messageHandlerRegistry.getHandlerDescriptors();

    // 按消息类型分组
    const messageTypes = new Set<string>();
    const incomeTypes: string[] = [];
    const outcomeTypes: string[] = [];

    for (const descriptor of handlerDescriptors) {
      messageTypes.add(descriptor.type);
      if (descriptor.direction === 'income') {
        incomeTypes.push(descriptor.type);
      } else {
        outcomeTypes.push(descriptor.type);
      }
    }

    // 基础通信服务
    services.push({
      id: `${did}#tunnel-communication`,
      type: 'TunnelCommunication',
      serviceEndpoint: 'tunnel://communication',
      description: 'P2P tunnel communication service',
      supportedMessageTypes: Array.from(messageTypes),
      capabilities: {
        income: incomeTypes,
        outcome: outcomeTypes,
        protocols: ['tunnel-v1', 'websocket']
      }
    });

    // 如果支持聊天相关消息，添加聊天服务
    const chatTypes = Array.from(messageTypes).filter(type => 
      type.includes('chat') || type.includes('completion')
    );
    
    if (chatTypes.length > 0) {
      services.push({
        id: `${did}#ai-chat`,
        type: 'AIChatService',
        serviceEndpoint: 'tunnel://ai-chat',
        description: 'AI chat and completion service',
        supportedMessageTypes: chatTypes,
        capabilities: {
          streaming: chatTypes.some(type => type.includes('stream')),
          models: [], // 将在运行时填充
          formats: ['openai', 'ollama']
        }
      });
    }

    return services;
  }

  /**
   * 构建AI推理服务
   */
  private buildInferenceServices(did: string, config?: DeviceCapabilityConfig): DidDocumentService[] {
    const services: DidDocumentService[] = [];

    if (config?.aiCapabilities) {
      const aiConfig = config.aiCapabilities;
      
      services.push({
        id: `${did}#ai-inference`,
        type: 'AIInferenceService',
        serviceEndpoint: 'tunnel://ai-inference',
        description: 'AI model inference service',
        capabilities: {
          models: aiConfig.supportedModels || [],
          maxContextLength: aiConfig.maxContextLength || 4096,
          supportedFormats: aiConfig.supportedFormats || ['text'],
          gpuAccelerated: aiConfig.gpuAccelerated || false,
          batchProcessing: aiConfig.batchProcessing || false,
          streaming: aiConfig.streaming || true
        }
      });
    }

    return services;
  }

  /**
   * 构建P2P网络服务
   */
  private buildNetworkServices(did: string, config?: DeviceCapabilityConfig): DidDocumentService[] {
    const services: DidDocumentService[] = [];

    // P2P路由服务
    services.push({
      id: `${did}#p2p-routing`,
      type: 'P2PRoutingService',
      serviceEndpoint: 'tunnel://p2p-routing',
      description: 'P2P message routing and relay service',
      capabilities: {
        maxHops: config?.networkCapabilities?.maxHops || 5,
        routingProtocols: ['whisper', 'gossip'],
        relayCapacity: config?.networkCapabilities?.relayCapacity || 100,
        bandwidthLimit: config?.networkCapabilities?.bandwidthLimit
      }
    });

    // 节点发现服务
    services.push({
      id: `${did}#node-discovery`,
      type: 'NodeDiscoveryService',
      serviceEndpoint: 'tunnel://node-discovery',
      description: 'P2P node discovery and connection service',
      capabilities: {
        discoveryMethods: ['dht', 'mdns', 'bootstrap'],
        maxConnections: config?.networkCapabilities?.maxConnections || 50,
        connectionTypes: ['websocket', 'webrtc']
      }
    });

    return services;
  }

  /**
   * 构建存储服务
   */
  private buildStorageServices(did: string, config?: DeviceCapabilityConfig): DidDocumentService[] {
    const services: DidDocumentService[] = [];

    if (config?.storageCapabilities) {
      const storageConfig = config.storageCapabilities;
      
      services.push({
        id: `${did}#distributed-storage`,
        type: 'DistributedStorageService',
        serviceEndpoint: 'tunnel://distributed-storage',
        description: 'Distributed data storage service',
        capabilities: {
          capacity: storageConfig.capacity,
          replication: storageConfig.replication || 3,
          encryption: storageConfig.encryption || true,
          compression: storageConfig.compression || true,
          supportedTypes: storageConfig.supportedTypes || ['file', 'object', 'kv']
        }
      });
    }

    return services;
  }

  /**
   * 获取设备能力摘要
   */
  getCapabilitySummary(services: DidDocumentService[]): DeviceCapabilitySummary {
    const summary: DeviceCapabilitySummary = {
      totalServices: services.length,
      serviceTypes: [],
      communicationProtocols: new Set(),
      aiCapabilities: {
        hasInference: false,
        supportedModels: [],
        supportedFormats: []
      },
      networkCapabilities: {
        hasRouting: false,
        hasDiscovery: false,
        maxConnections: 0
      },
      storageCapabilities: {
        hasStorage: false,
        capacity: 0
      }
    };

    for (const service of services) {
      summary.serviceTypes.push(service.type);

      // 分析通信协议
      if (service.capabilities?.protocols) {
        service.capabilities.protocols.forEach(protocol => 
          summary.communicationProtocols.add(protocol)
        );
      }

      // 分析AI能力
      if (service.type === 'AIInferenceService' || service.type === 'AIChatService') {
        summary.aiCapabilities.hasInference = true;
        if (service.capabilities?.models) {
          summary.aiCapabilities.supportedModels.push(...service.capabilities.models);
        }
        if (service.capabilities?.supportedFormats) {
          summary.aiCapabilities.supportedFormats.push(...service.capabilities.supportedFormats);
        }
      }

      // 分析网络能力
      if (service.type === 'P2PRoutingService') {
        summary.networkCapabilities.hasRouting = true;
      }
      if (service.type === 'NodeDiscoveryService') {
        summary.networkCapabilities.hasDiscovery = true;
        if (service.capabilities?.maxConnections) {
          summary.networkCapabilities.maxConnections = Math.max(
            summary.networkCapabilities.maxConnections,
            service.capabilities.maxConnections
          );
        }
      }

      // 分析存储能力
      if (service.type === 'DistributedStorageService') {
        summary.storageCapabilities.hasStorage = true;
        if (service.capabilities?.capacity) {
          summary.storageCapabilities.capacity += service.capabilities.capacity;
        }
      }
    }

    return summary;
  }
}

/**
 * 设备能力配置接口
 */
export interface DeviceCapabilityConfig {
  /** AI推理能力配置 */
  aiCapabilities?: {
    supportedModels?: string[];
    maxContextLength?: number;
    supportedFormats?: string[];
    gpuAccelerated?: boolean;
    batchProcessing?: boolean;
    streaming?: boolean;
  };

  /** 网络能力配置 */
  networkCapabilities?: {
    maxHops?: number;
    relayCapacity?: number;
    bandwidthLimit?: number;
    maxConnections?: number;
  };

  /** 存储能力配置 */
  storageCapabilities?: {
    capacity: number;
    replication?: number;
    encryption?: boolean;
    compression?: boolean;
    supportedTypes?: string[];
  };

  /** 自定义服务 */
  customServices?: DidDocumentService[];
}

/**
 * 设备能力摘要
 */
export interface DeviceCapabilitySummary {
  totalServices: number;
  serviceTypes: string[];
  communicationProtocols: Set<string>;
  aiCapabilities: {
    hasInference: boolean;
    supportedModels: string[];
    supportedFormats: string[];
  };
  networkCapabilities: {
    hasRouting: boolean;
    hasDiscovery: boolean;
    maxConnections: number;
  };
  storageCapabilities: {
    hasStorage: boolean;
    capacity: number;
  };
}
