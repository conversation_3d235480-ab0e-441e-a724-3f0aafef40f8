import { Injectable, Logger } from '@nestjs/common';
import {
  OllamaChatRequest,
  OllamaGenerateRequest,
  OllamaChatResponse,
  OllamaGenerateResponse,
  OpenAIChatCompletionRequest,
  OpenAICompletionRequest,
  OpenAIChatCompletionChunk,
  OpenAIChatCompletionResponse,
  OpenAICompletionResponse
} from '@saito/models';

/**
 * Ollama 格式转换器
 * 职责：在Ollama格式和OpenAI格式之间进行转换
 * 设计原则：单一职责原则 - 只负责格式转换
 */
@Injectable()
export class OllamaFormatConverter {
  private readonly logger = new Logger(OllamaFormatConverter.name);

  /**
   * 将Ollama聊天请求转换为OpenAI格式
   */
  convertChatRequestToOpenAI(ollamaRequest: OllamaChatRequest): OpenAIChatCompletionRequest {
    this.logger.debug(`[OllamaFormatConverter] Converting Ollama chat request to OpenAI format`);

    return {
      model: ollamaRequest.model,
      messages: ollamaRequest.messages.map(msg => ({
        role: msg.role,
        content: msg.content
      })),
      stream: ollamaRequest.stream ?? true,
      temperature: ollamaRequest.temperature ?? 0.7,
      top_p: ollamaRequest.top_p ?? 1.0,
      // OpenAI特有字段，提供默认值
      n: 1,
      presence_penalty: 0,
      frequency_penalty: 0,
      // 可选字段
      max_tokens: ollamaRequest['max_tokens'] as number | undefined,
      stop: ollamaRequest['stop'] as string | string[] | undefined,
      user: ollamaRequest.userId
    };
  }

  /**
   * 将Ollama生成请求转换为OpenAI Completion格式
   */
  convertGenerateRequestToOpenAI(ollamaRequest: OllamaGenerateRequest): OpenAICompletionRequest {
    this.logger.debug(`[OllamaFormatConverter] Converting Ollama generate request to OpenAI completion format`);

    return {
      model: ollamaRequest.model,
      prompt: ollamaRequest.prompt,
      stream: ollamaRequest.stream ?? true,
      temperature: ollamaRequest.temperature ?? 0.7,
      top_p: ollamaRequest.top_p ?? 1.0,
      // OpenAI特有字段，提供默认值
      n: 1,
      max_tokens: ollamaRequest['max_tokens'] ? Number(ollamaRequest['max_tokens']) : 16, // 使用OpenAI默认值
      stop: ollamaRequest['stop'] as string | string[] | undefined,
      echo: false,
      logprobs: null,
      presence_penalty: 0,
      frequency_penalty: 0,
      best_of: 1,
      user: ollamaRequest.userId
    };
  }

  /**
   * 将OpenAI聊天响应块转换为Ollama格式
   */
  convertOpenAIChatChunkToOllama(
    openaiChunk: OpenAIChatCompletionChunk,
    isComplete: boolean = false
  ): OllamaChatResponse {
    this.logger.debug(`[OllamaFormatConverter] Converting OpenAI chat chunk to Ollama format, done: ${isComplete}`);

    const choice = openaiChunk.choices?.[0];
    const delta = choice?.delta;

    return {
      model: openaiChunk.model,
      created_at: new Date().toISOString(),
      message: {
        role: 'assistant', // delta中没有role字段，固定为assistant
        content: delta?.content || ''
      },
      done: isComplete || choice?.finish_reason === 'stop',
      done_reason: choice?.finish_reason || undefined,
      // 性能指标 - 从OpenAI usage转换
      total_duration: undefined,
      load_duration: undefined,
      prompt_eval_count: openaiChunk.usage?.prompt_tokens,
      prompt_eval_duration: undefined,
      eval_count: openaiChunk.usage?.completion_tokens,
      eval_duration: undefined
    };
  }

  /**
   * 将OpenAI完整聊天响应转换为Ollama格式
   */
  convertOpenAIChatResponseToOllama(openaiResponse: OpenAIChatCompletionResponse): OllamaChatResponse {
    this.logger.debug(`[OllamaFormatConverter] Converting OpenAI chat response to Ollama format`);

    const choice = openaiResponse.choices?.[0];

    return {
      model: 'unknown', // OpenAICompletionResponse中没有model字段
      created_at: new Date().toISOString(),
      message: {
        role: choice?.message?.role || 'assistant',
        content: choice?.message?.content || ''
      },
      done: true,
      done_reason: choice?.finish_reason || 'stop',
      // 性能指标
      total_duration: undefined,
      load_duration: undefined,
      prompt_eval_count: openaiResponse.usage?.prompt_tokens,
      prompt_eval_duration: undefined,
      eval_count: openaiResponse.usage?.completion_tokens,
      eval_duration: undefined
    };
  }

  /**
   * 将OpenAI Completion响应块转换为Ollama生成格式
   */
  convertOpenAICompletionChunkToOllama(
    openaiChunk: any, // 使用any类型，因为没有专门的OpenAICompletionChunk类型
    isComplete: boolean = false
  ): OllamaGenerateResponse {
    this.logger.debug(`[OllamaFormatConverter] Converting OpenAI completion chunk to Ollama generate format, done: ${isComplete}`);

    const choice = openaiChunk.choices?.[0];

    return {
      model: openaiChunk.model,
      created_at: new Date().toISOString(),
      response: choice?.text || '',
      done: isComplete || choice?.finish_reason === 'stop',
      done_reason: choice?.finish_reason || undefined,
      // 性能指标
      total_duration: undefined,
      load_duration: undefined,
      prompt_eval_count: openaiChunk.usage?.prompt_tokens,
      prompt_eval_duration: undefined,
      eval_count: openaiChunk.usage?.completion_tokens,
      eval_duration: undefined
    };
  }

  /**
   * 将OpenAI完整Completion响应转换为Ollama生成格式
   */
  convertOpenAICompletionResponseToOllama(openaiResponse: OpenAICompletionResponse): OllamaGenerateResponse {
    this.logger.debug(`[OllamaFormatConverter] Converting OpenAI completion response to Ollama generate format`);

    const choice = openaiResponse.choices?.[0];

    return {
      model: openaiResponse.model || 'unknown',
      created_at: new Date().toISOString(),
      response: choice?.text || '',
      done: true,
      done_reason: choice?.finish_reason || 'stop',
      // 性能指标
      total_duration: undefined,
      load_duration: undefined,
      prompt_eval_count: openaiResponse.usage?.prompt_tokens,
      prompt_eval_duration: undefined,
      eval_count: openaiResponse.usage?.completion_tokens,
      eval_duration: undefined
    };
  }

  /**
   * 过滤内容，移除 <think> 标签
   */
  filterThinkTags(content: string): string {
    if (!content) return content;

    // 移除 <think> 和 </think> 标签及其内容
    return content
      .replace(/<think>[\s\S]*?<\/think>/gi, '')
      .replace(/<think>/gi, '')
      .replace(/<\/think>/gi, '')
      .trim();
  }

  /**
   * 检测响应数据格式（OpenAI Chat vs Completion）
   */
  detectResponseFormat(data: unknown): 'chat' | 'completion' | 'unknown' {
    if (!data || typeof data !== 'object') {
      return 'unknown';
    }

    const obj = data as Record<string, unknown>;

    // OpenAI Chat格式检测
    if (obj['choices'] && Array.isArray(obj['choices'])) {
      const firstChoice = obj['choices'][0] as Record<string, unknown>;
      if (firstChoice?.['message'] || firstChoice?.['delta']) {
        return 'chat';
      }
      if (firstChoice?.['text'] !== undefined) {
        return 'completion';
      }
    }

    return 'unknown';
  }
}
