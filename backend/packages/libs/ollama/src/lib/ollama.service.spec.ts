import { Test, TestingModule } from '@nestjs/testing';
import { OllamaServiceImpl } from './ollama.service';
import { TunnelService, ResponseAdapterService } from '@saito/tunnel';
import { TaskManager } from '@saito/task-manager';
import { NodeService } from '@saito/node';

describe('OllamaServiceImpl', () => {
  let service: OllamaServiceImpl;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OllamaServiceImpl,
        {
          provide: 'TunnelService',
          useValue: {
            handleMessage: jest.fn(),
            sendMessage: jest.fn(),
          },
        },
        {
          provide: 'TaskManager',
          useValue: {
            createTask: jest.fn(),
          },
        },
        {
          provide: NodeService,
          useValue: {
            findAvailableNodes: jest.fn(),
          },
        },
        {
          provide: 'PEER_ID',
          useValue: 'test-peer-id',
        },
        {
          provide: 'ResponseAdapterService',
          useValue: {
            registerTaskService: jest.fn(),
            handleStreamResponse: jest.fn(),
            handleErrorResponse: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<OllamaServiceImpl>(OllamaServiceImpl);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should have correct dependencies injected', () => {
    expect(service).toBeInstanceOf(OllamaServiceImpl);
  });
});
