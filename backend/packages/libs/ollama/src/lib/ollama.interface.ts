import { Response, Request } from 'express';
import { OllamaChatRequest, OllamaGenerateRequest } from '@saito/models';

/**
 * Ollama 服务接口
 * 职责：定义 Ollama 服务的核心方法
 * 设计原则：单一职责原则，只负责 Ollama 相关的业务逻辑
 */
export interface OllamaService {
  /**
   * 处理聊天请求
   * @param path 请求路径
   * @param body 请求体
   * @param res 响应对象
   * @param req 请求对象（可选，用于获取用户信息）
   */
  handleChat(path: string, body: OllamaChatRequest, res: Response, req?: Request): Promise<void>;

  /**
   * 处理生成请求
   * @param path 请求路径
   * @param body 请求体
   * @param res 响应对象
   * @param req 请求对象（可选，用于获取用户信息）
   */
  handleGenerate(path: string, body: OllamaGenerateRequest, res: Response, req?: Request): Promise<void>;

  /**
   * 代理请求
   * @param req 请求对象
   */
  proxyRequest(req: Request): Promise<any>;
}
