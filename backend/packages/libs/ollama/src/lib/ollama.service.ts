import { Injectable, Logger, HttpException, HttpStatus, OnModuleInit, Inject } from '@nestjs/common';
import { Response, Request } from 'express';
import { OllamaService as IOllamaService } from './ollama.interface';
import {
  OllamaChatRequest,
  OllamaGenerateRequest,
  InferenceError
} from '@saito/models';
import { ResponseAdapterService, StreamResponseHandler } from '@saito/tunnel';
import { OllamaRequestHandler } from './handlers/ollama-request.handler';
import { OllamaFormatConverter } from './handlers/ollama-format.converter';
import { OllamaMessageBuilder } from './handlers/ollama-message.builder';
import { OllamaResponseManager } from './handlers/ollama-response.manager';
import { OllamaDataProcessor } from './handlers/ollama-data.processor';
import { OllamaErrorHandler } from './handlers/ollama-error.handler';

/**
 * Ollama 服务实现 - 重构后的简化版本
 * 职责：协调各个处理器组件，实现接口，处理Ollama格式转换
 * 设计原则：
 * - 单一职责：只负责组件协调和格式转换
 * - 依赖倒置：依赖于抽象的处理器组件
 * - 接口隔离：只实现必要的 Ollama 功能
 */
@Injectable()
export class OllamaServiceImpl implements IOllamaService, StreamResponseHandler, OnModuleInit {
  private readonly logger = new Logger(OllamaServiceImpl.name);

  constructor(
    @Inject('ResponseAdapterService') private readonly responseAdapter: ResponseAdapterService,
    private readonly requestHandler: OllamaRequestHandler,
    private readonly formatConverter: OllamaFormatConverter,
    private readonly messageBuilder: OllamaMessageBuilder,
    private readonly responseManager: OllamaResponseManager,
    private readonly dataProcessor: OllamaDataProcessor,
    private readonly errorHandler: OllamaErrorHandler
  ) {}

  /**
   * 模块初始化时注册自己到响应适配器
   */
  onModuleInit() {
    this.responseAdapter.registerServiceHandler('ollama', this);
    this.logger.log('[OllamaService] Registered as stream response handler');
  }

  /**
   * 处理聊天请求 - 委托给请求处理器
   */
  async handleChat(_path: string, body: OllamaChatRequest, res: Response): Promise<void> {
    await this.requestHandler.handleChat(
      _path,
      body,
      res,
      this.formatConverter,
      this.messageBuilder,
      this.responseManager,
      this.errorHandler
    );
  }

  /**
   * 处理生成请求 - 委托给请求处理器
   */
  async handleGenerate(_path: string, body: OllamaGenerateRequest, res: Response): Promise<void> {
    await this.requestHandler.handleGenerate(
      _path,
      body,
      res,
      this.formatConverter,
      this.messageBuilder,
      this.responseManager,
      this.errorHandler
    );
  }

  /**
   * 代理请求 - 暂未实现
   */
  async proxyRequest(_req: Request): Promise<never> {
    const errorResponse = this.errorHandler.createNotImplementedError('Proxy request not implemented');
    throw new HttpException(errorResponse, HttpStatus.NOT_IMPLEMENTED);
  }

  // StreamResponseHandler 接口实现 - 委托给数据处理器

  /**
   * 处理来自设备的流式响应 - 委托给数据处理器
   */
  async handleStreamResponse(taskId: string, payload: unknown): Promise<void> {
    await this.dataProcessor.handleStreamResponse(taskId, payload);
  }

  /**
   * 处理来自设备的非流式响应 - 委托给数据处理器
   */
  async handleNonStreamResponse(taskId: string, payload: unknown): Promise<void> {
    await this.dataProcessor.handleNonStreamResponse(taskId, payload);
  }

  /**
   * 处理来自设备的流式 Completion 响应 - 委托给数据处理器
   */
  async handleCompletionStreamResponse(taskId: string, payload: unknown): Promise<void> {
    await this.dataProcessor.handleCompletionStreamResponse(taskId, payload);
  }

  /**
   * 处理来自设备的非流式 Completion 响应 - 委托给数据处理器
   */
  async handleCompletionNonStreamResponse(taskId: string, payload: unknown): Promise<void> {
    await this.dataProcessor.handleCompletionNonStreamResponse(taskId, payload);
  }

  /**
   * 处理错误响应 - 委托给错误处理器
   */
  async handleErrorResponse(taskId: string, error: InferenceError): Promise<void> {
    await this.errorHandler.handleErrorResponse(taskId, error);
  }
}

export const OllamaServiceProvider = {
  provide: 'OllamaService',
  useClass: OllamaServiceImpl,
};
