import { Injectable, Logger } from '@nestjs/common';
import { Task, CreateTaskRequest, TaskStatus, ChatNode, TaskMetrics } from '@saito/models';
import { PersistentService } from '@saito/persistent';
import { EarningsRepository } from '@saito/earnings';
import { TaskRepository } from './task-manager.repository';
import { SimpleTaskQueue } from './queue/simple-task-queue.service';
import { TaskEventListeners } from './task-manager.interface';
import { NodeService } from '@saito/node';

/**
 * 任务核心服务
 * 职责：统一处理任务的生命周期管理、执行协调、路由选择
 * 设计理念：将复杂的抽象简化为一个核心服务，减少文件数量
 */
@Injectable()
export class TaskCoreService {
  private readonly logger = new Logger(TaskCoreService.name);
  
  // 内存中的任务上下文存储
  private readonly taskContexts = new Map<string, TaskContext>();
  private readonly taskListeners = new Map<string, TaskEventListeners>();

  constructor(
    private readonly taskRepository: TaskRepository,
    private readonly persistentService: PersistentService,
    private readonly earningsRepository: EarningsRepository,
    private readonly simpleTaskQueue: SimpleTaskQueue,
    private readonly nodeService: NodeService
  ) {}

  /**
   * 创建任务
   */
  async createTask(request: CreateTaskRequest): Promise<Task> {
    return await this.persistentService.pgPool.transaction(async (conn) => {
      const task = await this.taskRepository.createTask(
        conn,
        request.device_id,
        request.model,
        request.user_id
      );
      
      // 创建任务上下文
      this.createTaskContext(task);
      
      this.logger.log(`Task ${task.id} created successfully`);
      return task;
    });
  }

  /**
   * 获取任务
   */
  async getTask(taskId: string): Promise<Task | null> {
    return await this.persistentService.pgPool.transaction(async (conn) => {
      return await this.taskRepository.getTask(conn, taskId);
    });
  }

  /**
   * 更新任务状态
   */
  async updateTaskStatus(taskId: string, status: TaskStatus): Promise<Task> {
    const task = await this.persistentService.pgPool.transaction(async (conn) => {
      switch (status) {
        case 'running':
          await this.taskRepository.startTask(conn, taskId);
          break;
        case 'failed':
          await this.taskRepository.failTask(conn, taskId);
          break;
        case 'cancelled':
          await this.taskRepository.cancelTask(conn, taskId);
          break;
        default:
          await this.taskRepository.updateTask(taskId, status);
          break;
      }
      return await this.taskRepository.getTask(conn, taskId);
    });

    if (!task) {
      throw new Error(`Task ${taskId} not found after status update`);
    }

    // 更新上下文
    this.updateTaskContext(taskId, { status });
    
    this.logger.log(`Task ${taskId} status updated to ${status}`);
    return task;
  }

  /**
   * 完成任务并记录收益
   */
  async completeTask(taskId: string, metrics: TaskMetrics): Promise<void> {
    await this.persistentService.pgPool.transaction(async (conn) => {
      // 更新任务状态和指标
      await this.taskRepository.completeTask(conn, taskId, metrics);

      // 获取任务详情用于创建收益记录
      const task = await this.taskRepository.getTask(conn, taskId);
      if (task?.device_id) {
        try {
          await this.earningsRepository.createEarningsRecord(taskId, task.device_id, metrics);
          this.logger.log(`Created earnings record for task ${taskId}`);
        } catch (error) {
          // 收益记录失败不应该影响任务完成
          this.logger.error(`Failed to create earnings record for task ${taskId}: ${error}`);
        }
      }
    });

    // 更新上下文
    this.updateTaskContext(taskId, { 
      status: 'completed', 
      completedAt: new Date(),
      metrics 
    });

    this.logger.log(`Task ${taskId} completed successfully`);
  }

  /**
   * 调度任务执行
   */
  async scheduleTask(task: Task, listeners?: TaskEventListeners): Promise<void> {
    try {
      // 存储监听器
      if (listeners) {
        this.taskListeners.set(task.id, listeners);
      }

      // 查找合适的节点
      const targetNode = await this.findBestNode(task);
      if (!targetNode) {
        throw new Error('No suitable node found');
      }

      // 更新任务状态为运行中
      await this.updateTaskStatus(task.id, 'running');

      // 更新上下文
      this.updateTaskContext(task.id, { 
        assignedNode: targetNode,
        startedAt: new Date()
      });

      // 执行任务
      await this.executeTask(task, targetNode, listeners);

      this.logger.log(`Task ${task.id} scheduled and executed successfully`);
    } catch (error) {
      this.logger.error(`Failed to schedule task ${task.id}: ${error}`);
      await this.updateTaskStatus(task.id, 'failed');
      throw error;
    }
  }

  /**
   * 外部触发任务失败
   */
  async failTaskExternally(taskId: string, reason?: string): Promise<Task> {
    this.logger.log(`Externally failing task ${taskId}${reason ? `: ${reason}` : ''}`);
    
    // 更新上下文
    this.updateTaskContext(taskId, { 
      error: reason,
      completedAt: new Date()
    });

    return await this.updateTaskStatus(taskId, 'failed');
  }

  /**
   * 获取任务完整信息
   */
  async getTaskInfo(taskId: string): Promise<TaskInfo | null> {
    const task = await this.getTask(taskId);
    if (!task) {
      return null;
    }

    const context = this.taskContexts.get(taskId);
    
    return {
      ...task,
      execution_status: context?.status,
      assigned_node: context?.assignedNode?.node_id,
      error_message: context?.error,
      started_at: context?.startedAt,
      completed_at: context?.completedAt
    };
  }

  /**
   * 查找最佳节点（简化版本）
   */
  private async findBestNode(task: Task): Promise<ChatNode | null> {
    try {
      // 获取所有可用节点
      const nodes = await this.nodeService.findAvailableNodes();
      if (!nodes || nodes.length === 0) {
        return null;
      }

      // 简单的随机选择策略
      const randomIndex = Math.floor(Math.random() * nodes.length);
      return nodes[randomIndex];
    } catch (error) {
      this.logger.error(`Failed to find best node: ${error}`);
      return null;
    }
  }

  /**
   * 执行任务的具体实现
   * 职责：协调任务执行的完整流程，包括消息发送、响应处理、状态更新
   */
  private async executeTask(task: Task, node: ChatNode, listeners?: TaskEventListeners): Promise<void> {
    const context = this.taskContexts.get(task.id);
    if (!context) {
      throw new Error(`Task context not found for task ${task.id}`);
    }

    try {
      this.logger.log(`Starting execution of task ${task.id} on node ${node.node_id}`);

      // 1. 准备执行环境
      await this.prepareTaskExecution(task, node);

      // 2. 发送任务到节点
      await this.sendTaskToNode(task, node, listeners);

      // 3. 监控任务执行
      await this.monitorTaskExecution(task);

      this.logger.log(`Task ${task.id} execution initiated successfully`);

    } catch (error) {
      this.logger.error(`Failed to execute task ${task.id}: ${error}`);

      // 更新任务状态为失败
      await this.updateTaskStatus(task.id, 'failed');

      // 更新上下文
      this.updateTaskContext(task.id, {
        error: error instanceof Error ? error.message : String(error),
        completedAt: new Date()
      });

      // 通知监听器
      if (listeners?.onError) {
        listeners.onError(error instanceof Error ? error : new Error(String(error)));
      }

      throw error;
    }
  }

  /**
   * 准备任务执行环境
   */
  private async prepareTaskExecution(task: Task, node: ChatNode): Promise<void> {
    // 更新上下文状态
    this.updateTaskContext(task.id, {
      assignedNode: node,
      startedAt: new Date()
    });

    // 设置任务超时
    this.setupTaskTimeout(task.id);

    this.logger.log(`Task ${task.id} execution environment prepared`);
  }

  /**
   * 发送任务到节点执行
   */
  private async sendTaskToNode(task: Task, node: ChatNode, listeners?: TaskEventListeners): Promise<void> {
    try {
      // 构建任务消息
      const taskMessage = this.buildTaskMessage(task, node);

      // 这里应该通过 tunnel 模块发送消息到节点
      // 由于我们在 task-manager 中，不直接依赖 tunnel，所以使用事件机制
      await this.sendMessageToTunnel(taskMessage, listeners);

      this.logger.log(`Task ${task.id} sent to node ${node.node_id}`);

    } catch (error) {
      this.logger.error(`Failed to send task ${task.id} to node: ${error}`);
      throw error;
    }
  }

  /**
   * 监控任务执行状态
   */
  private async monitorTaskExecution(task: Task): Promise<void> {
    // 设置执行监控
    const monitoringInterval = setInterval(() => {
      const context = this.taskContexts.get(task.id);
      if (!context) {
        clearInterval(monitoringInterval);
        return;
      }

      // 检查任务是否已完成
      if (context.status === 'completed' || context.status === 'failed' || context.status === 'cancelled') {
        clearInterval(monitoringInterval);
        return;
      }

      // 检查是否超时
      const now = new Date();
      const executionTime = context.startedAt ? now.getTime() - context.startedAt.getTime() : 0;
      const timeoutMs = 5 * 60 * 1000; // 5分钟超时

      if (executionTime > timeoutMs) {
        this.logger.warn(`Task ${task.id} execution timeout after ${executionTime}ms`);
        this.handleTaskTimeout(task.id);
        clearInterval(monitoringInterval);
      }
    }, 10000); // 每10秒检查一次

    this.logger.log(`Task ${task.id} monitoring started`);
  }

  /**
   * 构建发送给节点的任务消息
   */
  private buildTaskMessage(task: Task, node: ChatNode): any {
    return {
      type: 'CHAT_REQUEST',
      payload: {
        taskId: task.id,
        deviceId: task.device_id,
        model: task.model,
        userId: task.user_id,
        nodeId: node.node_id,
        timestamp: new Date().toISOString(),
        // 这里可以添加更多任务相关的参数
        options: {
          stream: true, // 默认使用流式响应
          timeout: 300000 // 5分钟超时
        }
      }
    };
  }

  /**
   * 通过事件机制发送消息到 tunnel 模块
   */
  private async sendMessageToTunnel(message: any, listeners?: TaskEventListeners): Promise<void> {
    try {
      // 这里应该通过某种机制（如事件总线、消息队列等）发送到 tunnel 模块
      // 由于架构简化，我们可以直接调用相关服务或使用依赖注入

      // 临时实现：记录消息并模拟发送
      this.logger.log(`Sending message to tunnel: ${JSON.stringify(message)}`);

      // 存储监听器以便后续回调
      if (listeners) {
        this.taskListeners.set(message.payload.taskId, listeners);
      }

      // TODO: 实际实现应该调用 tunnel 服务的发送方法
      // await this.tunnelService.sendMessage(message);

    } catch (error) {
      this.logger.error(`Failed to send message to tunnel: ${error}`);
      throw error;
    }
  }

  /**
   * 设置任务超时处理
   */
  private setupTaskTimeout(taskId: string): void {
    const timeoutMs = 5 * 60 * 1000; // 5分钟超时

    setTimeout(() => {
      const context = this.taskContexts.get(taskId);
      if (context && !context.isCompleted()) {
        this.logger.warn(`Task ${taskId} timed out after ${timeoutMs}ms`);
        this.handleTaskTimeout(taskId);
      }
    }, timeoutMs);
  }

  /**
   * 处理任务超时
   */
  private async handleTaskTimeout(taskId: string): Promise<void> {
    try {
      await this.updateTaskStatus(taskId, 'failed');

      this.updateTaskContext(taskId, {
        error: 'Task execution timeout',
        completedAt: new Date()
      });

      // 通知监听器
      const listeners = this.taskListeners.get(taskId);
      if (listeners?.onError) {
        listeners.onError(new Error('Task execution timeout'));
      }

      // 清理监听器
      this.taskListeners.delete(taskId);

      this.logger.log(`Task ${taskId} marked as failed due to timeout`);

    } catch (error) {
      this.logger.error(`Failed to handle timeout for task ${taskId}: ${error}`);
    }
  }

  /**
   * 创建任务上下文
   */
  private createTaskContext(task: Task): void {
    const context = new TaskContext(task.id, task.status as any);
    this.taskContexts.set(task.id, context);
  }

  /**
   * 更新任务上下文
   */
  private updateTaskContext(taskId: string, updates: Partial<TaskContext>): void {
    const context = this.taskContexts.get(taskId);
    if (context) {
      Object.assign(context, updates);
    }
  }

  /**
   * 处理来自节点的流式数据（供外部调用）
   */
  async handleTaskStreamData(taskId: string, data: any): Promise<void> {
    const listeners = this.taskListeners.get(taskId);
    if (listeners?.onData) {
      listeners.onData(data);
    }

    this.logger.debug(`Task ${taskId} received stream data`);
  }

  /**
   * 处理任务完成响应（供外部调用）
   */
  async handleTaskCompleteResponse(taskId: string, result: any, metrics?: TaskMetrics): Promise<void> {
    try {
      // 如果有指标数据，完成任务并记录收益
      if (metrics) {
        await this.completeTask(taskId, metrics);
      } else {
        // 否则只更新状态
        await this.updateTaskStatus(taskId, 'completed');
      }

      // 通知监听器
      const listeners = this.taskListeners.get(taskId);
      if (listeners?.onComplete) {
        listeners.onComplete(result);
      }

      // 清理监听器
      this.taskListeners.delete(taskId);

      this.logger.log(`Task ${taskId} completed successfully`);

    } catch (error) {
      this.logger.error(`Failed to handle task completion for ${taskId}: ${error}`);
      await this.handleTaskErrorResponse(taskId, error instanceof Error ? error : new Error(String(error)));
    }
  }

  /**
   * 处理任务错误响应（供外部调用）
   */
  async handleTaskErrorResponse(taskId: string, error: Error): Promise<void> {
    try {
      await this.updateTaskStatus(taskId, 'failed');

      // 更新上下文
      this.updateTaskContext(taskId, {
        error: error.message,
        completedAt: new Date()
      });

      // 通知监听器
      const listeners = this.taskListeners.get(taskId);
      if (listeners?.onError) {
        listeners.onError(error);
      }

      // 清理监听器
      this.taskListeners.delete(taskId);

      this.logger.log(`Task ${taskId} failed: ${error.message}`);

    } catch (err) {
      this.logger.error(`Failed to handle task error for ${taskId}: ${err}`);
    }
  }

  /**
   * 获取任务监听器（供外部模块使用）
   */
  getTaskListeners(taskId: string): TaskEventListeners | undefined {
    return this.taskListeners.get(taskId);
  }

  /**
   * 检查任务是否存在
   */
  hasTask(taskId: string): boolean {
    return this.taskContexts.has(taskId);
  }

  /**
   * 获取统计信息
   */
  getStats(): TaskStats {
    const contexts = Array.from(this.taskContexts.values());
    return {
      total: contexts.length,
      pending: contexts.filter(c => c.status === 'pending').length,
      running: contexts.filter(c => c.status === 'running').length,
      completed: contexts.filter(c => c.status === 'completed').length,
      failed: contexts.filter(c => c.status === 'failed').length,
      cancelled: contexts.filter(c => c.status === 'cancelled').length
    };
  }
}

/**
 * 任务上下文类（简化版本）
 */
class TaskContext {
  public taskId: string;
  public status: TaskStatus;
  public createdAt: Date;
  public assignedNode?: ChatNode;
  public startedAt?: Date;
  public completedAt?: Date;
  public error?: string;
  public metrics?: TaskMetrics;

  constructor(taskId: string, status: TaskStatus) {
    this.taskId = taskId;
    this.status = status;
    this.createdAt = new Date();
  }

  /**
   * 检查任务是否已完成
   */
  isCompleted(): boolean {
    return ['completed', 'failed', 'cancelled'].includes(this.status);
  }

  /**
   * 获取执行时长
   */
  getExecutionDuration(): number | null {
    if (!this.startedAt) return null;
    const endTime = this.completedAt || new Date();
    return endTime.getTime() - this.startedAt.getTime();
  }
}

/**
 * 任务信息（简化版本）
 */
export interface TaskInfo extends Task {
  execution_status?: TaskStatus;
  assigned_node?: string;
  error_message?: string;
  started_at?: Date;
  completed_at?: Date;
}



/**
 * 任务统计信息
 */
export interface TaskStats {
  total: number;
  pending: number;
  running: number;
  completed: number;
  failed: number;
  cancelled: number;
}
