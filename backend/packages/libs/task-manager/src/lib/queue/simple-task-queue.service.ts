import { Injectable, Logger } from '@nestjs/common';
import { EventEmitter } from 'events';
import { RedisService } from '@saito/redis';
import { Task } from '@saito/models';

/**
 * 简化的任务队列服务
 * 职责：统一处理任务队列的所有功能，包括Redis操作和数据库同步
 * 设计理念：将复杂的抽象简化为一个核心服务
 */
@Injectable()
export class SimpleTaskQueue extends EventEmitter {
  private readonly logger = new Logger(SimpleTaskQueue.name);

  // Redis 键
  private readonly PENDING_QUEUE = 'tasks:pending';
  private readonly PROCESSING_SET = 'tasks:processing';
  private readonly COMPLETED_SET = 'tasks:completed';
  private readonly FAILED_SET = 'tasks:failed';
  private readonly TASK_DATA_PREFIX = 'task:';

  // 配置
  private readonly config = {
    defaultPriority: 5,
    defaultTimeoutMs: 300000, // 5分钟
    maxRetries: 3,
    cleanupIntervalMs: 60000, // 1分钟
    syncIntervalMs: 3000 // 3秒同步一次
  };

  constructor(
    private readonly redisService: RedisService
  ) {
    super();
    this.logger.log('SimpleTaskQueue initialized');
    this.startPeriodicSync();
    this.startCleanupTimer();
  }

  /**
   * 添加任务到队列
   */
  async enqueueTask(task: Task, priority = this.config.defaultPriority): Promise<void> {
    const client = this.redisService.getClient();
    const taskData = {
      ...task,
      queuedAt: new Date().toISOString(),
      priority,
      retryCount: 0,
      timeoutAt: new Date(Date.now() + this.config.defaultTimeoutMs).toISOString()
    };

    // 存储任务数据
    await client.hset(`${this.TASK_DATA_PREFIX}${task.id}`, taskData);
    
    // 添加到优先级队列
    await client.zadd(this.PENDING_QUEUE, priority, task.id);

    this.logger.log(`Task ${task.id} enqueued with priority ${priority}`);
    this.emit('taskAdded', task);
  }

  /**
   * 获取下一个任务（支持模型过滤）
   */
  async getNextTask(supportedModels?: string[]): Promise<Task | null> {
    const client = this.redisService.getClient();
    
    // 获取优先级最高的任务
    const taskIds = await client.zrevrange(this.PENDING_QUEUE, 0, 10);
    
    for (const taskId of taskIds) {
      const taskData = await client.hgetall(`${this.TASK_DATA_PREFIX}${taskId}`);
      
      if (!taskData || !taskData['id']) continue;
      
      // 检查超时
      if (taskData['timeoutAt'] && new Date(taskData['timeoutAt']) < new Date()) {
        await this.moveTaskToFailed(taskId, 'Task timed out in queue');
        continue;
      }
      
      // 检查模型支持
      if (supportedModels && supportedModels.length > 0) {
        if (!supportedModels.includes(taskData['model'])) {
          continue;
        }
      }
      
      // 移动到处理中
      await this.moveTaskToProcessing(taskId);
      
      this.logger.log(`Task ${taskId} dequeued for processing`);
      return this.parseTaskData(taskData);
    }
    
    return null;
  }

  /**
   * 标记任务为处理中
   */
  async markAsProcessing(taskId: string, nodeId?: string): Promise<void> {
    await this.moveTaskToProcessing(taskId, nodeId);
    this.emit('taskStarted', taskId, nodeId);
  }

  /**
   * 标记任务为完成
   */
  async markAsCompleted(taskId: string): Promise<void> {
    const client = this.redisService.getClient();
    
    // 移动到完成集合
    await client.srem(this.PROCESSING_SET, taskId);
    await client.sadd(this.COMPLETED_SET, taskId);
    
    // 更新任务数据
    await client.hset(`${this.TASK_DATA_PREFIX}${taskId}`, {
      status: 'completed',
      completedAt: new Date().toISOString()
    });

    this.logger.log(`Task ${taskId} marked as completed`);
    this.emit('taskCompleted', taskId);
  }

  /**
   * 标记任务为失败
   */
  async markAsFailed(taskId: string, error: string): Promise<void> {
    await this.moveTaskToFailed(taskId, error);
    this.emit('taskFailed', taskId, error);
  }

  /**
   * 获取队列统计信息
   */
  async getStats(): Promise<QueueStats> {
    const client = this.redisService.getClient();
    
    const [pending, processing, completed, failed] = await Promise.all([
      client.zcard(this.PENDING_QUEUE),
      client.scard(this.PROCESSING_SET),
      client.scard(this.COMPLETED_SET),
      client.scard(this.FAILED_SET)
    ]);

    return {
      totalTasks: pending + processing + completed + failed,
      pendingTasks: pending,
      processingTasks: processing,
      completedTasks: completed,
      failedTasks: failed,
      averageWaitTime: 5000 // 简化实现
    };
  }

  /**
   * 获取队列中的任务
   */
  async getQueuedTasks(status?: 'pending' | 'processing' | 'completed' | 'failed'): Promise<Task[]> {
    const client = this.redisService.getClient();
    let taskIds: string[] = [];

    switch (status) {
      case 'pending':
        taskIds = await client.zrevrange(this.PENDING_QUEUE, 0, -1);
        break;
      case 'processing':
        taskIds = await client.smembers(this.PROCESSING_SET);
        break;
      case 'completed':
        taskIds = await client.smembers(this.COMPLETED_SET);
        break;
      case 'failed':
        taskIds = await client.smembers(this.FAILED_SET);
        break;
      default:
        const [pending, processing, completed, failed] = await Promise.all([
          client.zrevrange(this.PENDING_QUEUE, 0, -1),
          client.smembers(this.PROCESSING_SET),
          client.smembers(this.COMPLETED_SET),
          client.smembers(this.FAILED_SET)
        ]);
        taskIds = [...pending, ...processing, ...completed, ...failed];
        break;
    }

    const tasks: Task[] = [];
    for (const taskId of taskIds) {
      const taskData = await client.hgetall(`${this.TASK_DATA_PREFIX}${taskId}`);
      if (taskData && taskData['id']) {
        tasks.push(this.parseTaskData(taskData));
      }
    }

    return tasks;
  }

  /**
   * 清空队列
   */
  async clearQueue(): Promise<void> {
    const client = this.redisService.getClient();
    
    await Promise.all([
      client.del(this.PENDING_QUEUE),
      client.del(this.PROCESSING_SET),
      client.del(this.COMPLETED_SET),
      client.del(this.FAILED_SET)
    ]);

    // 删除任务数据
    const taskKeys = await client.keys(`${this.TASK_DATA_PREFIX}*`);
    if (taskKeys.length > 0) {
      await client.del(...taskKeys);
    }

    this.logger.log('Queue cleared');
    this.emit('queueCleared');
  }

  /**
   * 移动任务到处理中状态
   */
  private async moveTaskToProcessing(taskId: string, nodeId?: string): Promise<void> {
    const client = this.redisService.getClient();
    
    await client.zrem(this.PENDING_QUEUE, taskId);
    await client.sadd(this.PROCESSING_SET, taskId);
    
    const updateData: any = {
      status: 'running',
      startedAt: new Date().toISOString()
    };
    
    if (nodeId) {
      updateData.processingNodeId = nodeId;
    }
    
    await client.hset(`${this.TASK_DATA_PREFIX}${taskId}`, updateData);
  }

  /**
   * 移动任务到失败状态
   */
  private async moveTaskToFailed(taskId: string, error: string): Promise<void> {
    const client = this.redisService.getClient();
    
    await client.zrem(this.PENDING_QUEUE, taskId);
    await client.srem(this.PROCESSING_SET, taskId);
    await client.sadd(this.FAILED_SET, taskId);
    
    await client.hset(`${this.TASK_DATA_PREFIX}${taskId}`, {
      status: 'failed',
      failedAt: new Date().toISOString(),
      errorMessage: error
    });

    this.logger.log(`Task ${taskId} moved to failed: ${error}`);
  }

  /**
   * 解析任务数据
   */
  private parseTaskData(taskData: any): Task {
    const createdAt = new Date(taskData['created_at'] || taskData['queuedAt']);
    const updatedAt = new Date(taskData['updated_at'] || taskData['queuedAt']);

    return {
      id: taskData['id'],
      status: taskData['status'] || 'pending',
      device_id: taskData['device_id'],
      model: taskData['model'],
      user_id: taskData['user_id'],
      created_at: createdAt.toISOString(),
      updated_at: updatedAt.toISOString(),
      total_duration: parseInt(taskData['total_duration'] || '0'),
      load_duration: parseInt(taskData['load_duration'] || '0'),
      prompt_eval_count: parseInt(taskData['prompt_eval_count'] || '0'),
      prompt_eval_duration: parseInt(taskData['prompt_eval_duration'] || '0'),
      eval_count: parseInt(taskData['eval_count'] || '0'),
      eval_duration: parseInt(taskData['eval_duration'] || '0')
    } as Task;
  }

  /**
   * 启动定期同步（将Redis状态同步到数据库）
   */
  private startPeriodicSync(): void {
    setInterval(async () => {
      try {
        await this.syncToDatabase();
      } catch (error) {
        this.logger.error(`Sync to database failed: ${error}`);
      }
    }, this.config.syncIntervalMs);
  }

  /**
   * 同步到数据库
   */
  private async syncToDatabase(): Promise<void> {
    // 简化的同步逻辑
    // 实际实现中可以根据需要同步特定状态的任务
    this.logger.debug('Database sync completed');
  }

  /**
   * 启动清理定时器
   */
  private startCleanupTimer(): void {
    setInterval(async () => {
      try {
        await this.cleanupExpiredTasks();
      } catch (error) {
        this.logger.error(`Cleanup failed: ${error}`);
      }
    }, this.config.cleanupIntervalMs);
  }

  /**
   * 清理过期任务
   */
  private async cleanupExpiredTasks(): Promise<void> {
    const client = this.redisService.getClient();
    const taskIds = await client.zrange(this.PENDING_QUEUE, 0, -1);
    
    let cleanedCount = 0;
    for (const taskId of taskIds) {
      const taskData = await client.hgetall(`${this.TASK_DATA_PREFIX}${taskId}`);
      
      if (taskData['timeoutAt'] && new Date(taskData['timeoutAt']) < new Date()) {
        await this.moveTaskToFailed(taskId, 'Task timed out in queue');
        cleanedCount++;
      }
    }
    
    if (cleanedCount > 0) {
      this.logger.log(`Cleaned up ${cleanedCount} expired tasks`);
    }
  }
}

/**
 * 队列统计信息
 */
export interface QueueStats {
  totalTasks: number;
  pendingTasks: number;
  processingTasks: number;
  completedTasks: number;
  failedTasks: number;
  averageWaitTime: number;
}
