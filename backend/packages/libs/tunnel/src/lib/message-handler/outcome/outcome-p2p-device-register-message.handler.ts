import { Inject, Injectable, Logger } from '@nestjs/common';
import { OutcomeBaseMessageHandler } from '../base-message-handler';
import { TunnelMessage, P2PDeviceRegisterResponseMessage, P2PDeviceRegisterResponseMessageSchema } from '@saito/models';
import { MessageHandler } from '../message-handler.decorator';

/**
 * P2P设备注册响应消息处理器
 * 处理从Gateway返回的设备注册响应
 */
@MessageHandler({ type: 'p2p_device_register_response', direction: 'outcome' })
@Injectable()
export class OutcomeP2PDeviceRegisterResponseMessageHandler extends OutcomeBaseMessageHandler {
  private readonly logger = new Logger(OutcomeP2PDeviceRegisterResponseMessageHandler.name);
  
  // 存储待处理的注册请求回调
  private pendingRegistrations = new Map<string, RegistrationCallback>();

  constructor(
    @Inject('PEER_ID') protected override readonly peerId: string
  ) {
    super(peerId);
  }

  async handleOutcomeMessage(message: TunnelMessage): Promise<void> {
    try {
      this.logger.log(`Processing P2P device registration response from ${message.to}`);

      // 解析响应消息
      const responseMessage = P2PDeviceRegisterResponseMessageSchema.parse(message);
      const payload = responseMessage.payload;

      // 验证响应签名
      const isSignatureValid = await this.verifyResponseSignature(payload);
      if (!isSignatureValid) {
        this.logger.warn('Invalid response signature from gateway');
        return;
      }

      // 查找对应的注册请求回调
      const requestId = this.generateRequestId(message.to, payload.timestamp);
      const callback = this.pendingRegistrations.get(requestId);

      if (callback) {
        // 移除待处理的请求
        this.pendingRegistrations.delete(requestId);

        // 执行回调
        if (payload.success) {
          callback.resolve({
            success: true,
            nodeId: payload.nodeId!,
            status: payload.status!
          });
          this.logger.log(`Device registration successful: ${payload.nodeId}`);
        } else {
          callback.reject(new Error(payload.error || 'Registration failed'));
          this.logger.warn(`Device registration failed: ${payload.error}`);
        }
      } else {
        this.logger.warn(`No pending registration found for response from ${message.to}`);
      }
    } catch (error) {
      this.logger.error(`Failed to process P2P device registration response: ${error}`);
    }
  }

  /**
   * 注册设备注册请求回调
   * @param gatewayId Gateway ID
   * @param timestamp 请求时间戳
   * @param callback 回调函数
   */
  registerCallback(gatewayId: string, timestamp: number, callback: RegistrationCallback): void {
    const requestId = this.generateRequestId(gatewayId, timestamp);
    this.pendingRegistrations.set(requestId, callback);

    // 设置超时清理
    setTimeout(() => {
      if (this.pendingRegistrations.has(requestId)) {
        this.pendingRegistrations.delete(requestId);
        callback.reject(new Error('Registration request timeout'));
      }
    }, 30000); // 30秒超时
  }

  /**
   * 验证响应签名
   */
  private async verifyResponseSignature(payload: any): Promise<boolean> {
    try {
      // 提取签名和待验证数据
      const { signature, ...dataToVerify } = payload;
      
      // 这里应该使用Gateway的公钥验证签名
      // 暂时返回true，实际实现需要获取Gateway的DID文档并验证
      this.logger.debug('Response signature verification (placeholder)');
      return true;
    } catch (error) {
      this.logger.error(`Response signature verification failed: ${error}`);
      return false;
    }
  }

  /**
   * 生成请求ID
   */
  private generateRequestId(gatewayId: string, timestamp: number): string {
    return `${gatewayId}-${timestamp}`;
  }

  /**
   * 清理过期的待处理请求
   */
  private cleanupExpiredRequests(): void {
    const now = Date.now();
    const expiredThreshold = 60000; // 1分钟

    for (const [requestId, callback] of this.pendingRegistrations.entries()) {
      // 从requestId中提取时间戳
      const parts = requestId.split('-');
      const timestamp = parseInt(parts[parts.length - 1], 10);
      
      if (now - timestamp > expiredThreshold) {
        this.pendingRegistrations.delete(requestId);
        callback.reject(new Error('Registration request expired'));
      }
    }
  }

  /**
   * 获取待处理请求数量
   */
  getPendingRequestsCount(): number {
    return this.pendingRegistrations.size;
  }

  /**
   * 清理所有待处理请求
   */
  clearAllPendingRequests(): void {
    for (const callback of this.pendingRegistrations.values()) {
      callback.reject(new Error('Service shutdown'));
    }
    this.pendingRegistrations.clear();
  }
}

/**
 * 注册回调接口
 */
export interface RegistrationCallback {
  resolve: (result: RegistrationResult) => void;
  reject: (error: Error) => void;
}

/**
 * 注册结果接口
 */
export interface RegistrationResult {
  success: boolean;
  nodeId: string;
  status: string;
}
