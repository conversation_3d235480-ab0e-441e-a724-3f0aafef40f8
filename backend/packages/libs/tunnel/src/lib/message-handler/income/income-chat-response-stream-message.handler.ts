import {Inject, Injectable, Logger} from "@nestjs/common";
import {IncomeBaseMessageHandler} from "../base-message-handler";
import {StreamChatResponseMessageSchema, TunnelMessage, TaskMetrics} from "@saito/models";
import {MessageHandler} from "../message-handler.decorator";
import {TaskExecutionService} from "@saito/task-manager";
import {ResponseAdapterService} from "../../response-adapter.service";

@MessageHandler({ type: 'chat_response_stream', direction: 'income' })
@Injectable()
export class IncomeChatResponseStreamMessageHandler extends IncomeBaseMessageHandler {
  private readonly logger = new Logger(IncomeChatResponseStreamMessageHandler.name);

  // 消息去重：存储已处理的消息ID
  private readonly processedMessages = new Set<string>();

  constructor(
    @Inject('PEER_ID') protected override readonly peerId: string,
    @Inject('TaskExecutionService') private readonly taskExecutionService: TaskExecutionService,
    @Inject('ResponseAdapterService') private readonly responseAdapter: ResponseAdapterService
  ) {
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    // 调试：检查是否有 completion 任务被错误地路由到这里
    this.logger.debug(`[IncomeChatResponseStreamHandler] Received message type: ${message.type} from ${message.from}`);

    // 尝试解析标准格式
    const chatResponseMessage = StreamChatResponseMessageSchema.parse(message);
    const taskId = chatResponseMessage.payload.taskId;
    const data = chatResponseMessage.payload.data;

    // 调试：检查任务是否是 completion 类型
    this.logger.debug(`[IncomeChatResponseStreamHandler] Processing task ${taskId}, data object type: ${data.object || 'unknown'}`);

    // 如果这是一个 completion 响应但被错误地发送为 chat_response_stream，我们需要特殊处理
    if (data.object === 'text_completion') {
      this.logger.warn(`[IncomeChatResponseStreamHandler] Detected completion response sent as chat_response_stream for task ${taskId}. This should be handled by completion handler.`);
      // 暂时继续处理，但记录警告
    }

    // 生成消息唯一标识符用于去重（基于taskId和消息内容）
    const contentHash = data.choices?.[0]?.delta?.content || '';
    const finishReason = data.choices?.[0]?.finish_reason || 'streaming';
    const messageId = `${message.from}-${taskId}-${contentHash}-${finishReason}`;

    // 检查是否已经处理过这个消息
    if (this.processedMessages.has(messageId)) {
      this.logger.debug(`[IncomeChatResponseStreamHandler] Duplicate message detected for task ${taskId}, skipping processing`);
      return;
    }

    // 标记消息为已处理
    this.processedMessages.add(messageId);

    // 清理旧的消息ID（保留最近1000条）
    if (this.processedMessages.size > 1000) {
      const oldestIds = Array.from(this.processedMessages).slice(0, 500);
      oldestIds.forEach(id => this.processedMessages.delete(id));
    }

    this.logger.debug(`[IncomeChatResponseStreamHandler] Processing stream message for task ${taskId} with content: "${contentHash}"`);

    try {
      // 检查是否有服务注册了这个任务
      const hasRegisteredService = this.responseAdapter.hasRegisteredService(taskId);

      if (hasRegisteredService) {
        // 只有当有服务注册时才处理（OpenAI等旧架构）

        // 检查是否是 completion 格式的数据
        if (data.object === 'text_completion') {
          this.logger.debug(`[IncomeChatResponseStreamHandler] Detected completion data, calling handleCompletionStreamResponse for task ${taskId}`);
          await this.responseAdapter.handleCompletionStreamResponse(taskId, data);
        } else {
          this.logger.debug(`[IncomeChatResponseStreamHandler] Calling responseAdapter.handleStreamResponse for task ${taskId} with content: "${contentHash}"`);
          await this.responseAdapter.handleStreamResponse(taskId, data);
        }
      } else {
        // 没有注册的服务，说明是新架构（Ollama等），跳过处理
        this.logger.debug(`[IncomeChatResponseStreamHandler] No service registered for task ${taskId}, skipping ResponseAdapter processing (handled by new architecture)`);
      }

      // 如果是流的最后一条消息，处理任务完成
      if (data.choices?.[0]?.finish_reason === 'stop') {
        // Extract metrics from the response data
        const metrics: TaskMetrics = {
          total_duration: data.usage?.total_tokens || 0,
          load_duration: data.usage?.completion_tokens || 0,
          prompt_eval_count: data.usage?.prompt_tokens || 0,
          prompt_eval_duration: data.usage?.prompt_tokens || 0,
          eval_count: data.usage?.completion_tokens || 0,
          eval_duration: data.usage?.completion_tokens || 0
        };

        // Complete the task with metrics
        await this.taskExecutionService.completeTask(taskId, metrics);

        this.logger.log(`[IncomeChatResponseStreamHandler] Stream task ${taskId} completed successfully`);
      } else {
        // This is a streaming chunk
        this.logger.debug(`[IncomeChatResponseStreamHandler] Stream chunk for task ${taskId}: ${data.choices?.[0]?.delta?.content || 'no content'}`);
      }
    } catch (error) {
      this.logger.error(`[IncomeChatResponseStreamHandler] Error processing stream response for task ${taskId}: ${error instanceof Error ? error.message : String(error)}`);

      // 只有当有服务注册时才通知响应适配器处理错误
      if (this.responseAdapter.hasRegisteredService(taskId)) {
        await this.responseAdapter.handleErrorResponse(taskId, error);
      }

      // Mark task as failed
      await this.taskExecutionService.failTask(taskId, `Failed to process stream chat response: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
