import { Inject, Injectable, Logger } from '@nestjs/common';
import { IncomeBaseMessageHandler } from '../base-message-handler';
import { TunnelMessage, P2PDeviceRegisterMessage, P2PDeviceRegisterMessageSchema } from '@saito/models';
import { MessageHandler } from '../message-handler.decorator';
import { TunnelService } from '../../tunnel.service';
import { NodeService } from '@saito/node';
import { DidDocumentVerifier } from '@saito/did';

/**
 * P2P设备注册消息处理器
 * 处理通过tunnel发送的设备注册请求
 */
@MessageHandler({ type: 'p2p_device_register', direction: 'income' })
@Injectable()
export class IncomeP2PDeviceRegisterMessageHandler extends IncomeBaseMessageHandler {
  private readonly logger = new Logger(IncomeP2PDeviceRegisterMessageHandler.name);

  constructor(
    @Inject('TunnelService') private readonly tunnel: TunnelService,
    @Inject('PEER_ID') protected override readonly peerId: string,
    private readonly nodeService: NodeService,
    private readonly didVerifier: DidDocumentVerifier
  ) {
    super(peerId);
  }

  async handleIncomeMessage(message: TunnelMessage): Promise<void> {
    try {
      this.logger.log(`Processing P2P device registration from ${message.from}`);

      // 解析消息
      const registerMessage = P2PDeviceRegisterMessageSchema.parse(message);
      const payload = registerMessage.payload;

      // 验证消息签名
      const isSignatureValid = await this.verifyMessageSignature(payload);
      if (!isSignatureValid) {
        await this.sendErrorResponse(message.from, 'Invalid message signature');
        return;
      }

      // 验证DID文档
      const isDidValid = await this.verifyDidDocument(payload.didDocument);
      if (!isDidValid) {
        await this.sendErrorResponse(message.from, 'Invalid DID document');
        return;
      }

      // 验证DID与签名的一致性
      const isDidConsistent = await this.verifyDidConsistency(payload.did, payload.didDocument, payload.signature);
      if (!isDidConsistent) {
        await this.sendErrorResponse(message.from, 'DID and signature inconsistency');
        return;
      }

      // 处理设备注册
      const registrationResult = await this.processDeviceRegistration(payload);

      if (registrationResult.success) {
        await this.sendSuccessResponse(
          message.from,
          registrationResult.nodeId!,
          registrationResult.status!
        );
        this.logger.log(`Device registration successful: ${registrationResult.nodeId}`);
      } else {
        await this.sendErrorResponse(message.from, registrationResult.error || 'Registration failed');
        this.logger.warn(`Device registration failed: ${registrationResult.error}`);
      }
    } catch (error) {
      this.logger.error(`Failed to process P2P device registration: ${error}`);
      await this.sendErrorResponse(message.from, 'Internal server error');
    }
  }

  /**
   * 验证消息签名
   */
  private async verifyMessageSignature(payload: any): Promise<boolean> {
    try {
      // 提取签名和待验证数据
      const { signature, ...dataToVerify } = payload;
      
      // 创建待验证的数据字符串
      const dataString = JSON.stringify(dataToVerify);
      const dataBytes = new TextEncoder().encode(dataString);

      // 从DID文档中提取公钥
      const didDocument = payload.didDocument;
      if (!didDocument.verificationMethod || didDocument.verificationMethod.length === 0) {
        this.logger.warn('No verification method found in DID document');
        return false;
      }

      const verificationMethod = didDocument.verificationMethod[0];
      if (!verificationMethod.publicKeyMultibase) {
        this.logger.warn('No public key found in verification method');
        return false;
      }

      // 解码公钥
      const publicKeyBytes = this.decodeBase58(verificationMethod.publicKeyMultibase);
      
      // 验证签名
      const signatureBytes = Buffer.from(signature, 'base64');
      
      // 使用nacl验证签名
      const nacl = require('tweetnacl');
      const isValid = nacl.sign.detached.verify(dataBytes, signatureBytes, publicKeyBytes);

      this.logger.debug(`Signature verification result: ${isValid}`);
      return isValid;
    } catch (error) {
      this.logger.error(`Signature verification failed: ${error}`);
      return false;
    }
  }

  /**
   * 验证DID文档
   */
  private async verifyDidDocument(didDocument: any): Promise<boolean> {
    try {
      // 使用DID验证器验证文档
      const isValid = await this.didVerifier.verify(didDocument);
      this.logger.debug(`DID document verification result: ${isValid}`);
      return isValid;
    } catch (error) {
      this.logger.error(`DID document verification failed: ${error}`);
      return false;
    }
  }

  /**
   * 验证DID与签名的一致性
   */
  private async verifyDidConsistency(did: string, didDocument: any, signature: string): Promise<boolean> {
    try {
      // 检查DID文档的ID是否与声明的DID一致
      if (didDocument.id !== did) {
        this.logger.warn(`DID mismatch: declared=${did}, document=${didDocument.id}`);
        return false;
      }

      // 检查签名是否由DID文档中的密钥生成
      // 这里可以添加更多的一致性检查
      return true;
    } catch (error) {
      this.logger.error(`DID consistency check failed: ${error}`);
      return false;
    }
  }

  /**
   * 处理设备注册
   */
  private async processDeviceRegistration(payload: any): Promise<RegistrationResult> {
    try {
      // 构建传统的设备注册请求
      const deviceRegisterRequest = {
        code: payload.code,
        gateway_address: payload.gatewayAddress,
        reward_address: payload.rewardAddress,
        device_type: payload.deviceType,
        gpu_type: payload.gpuType,
        ip: payload.ipAddress,
        local_models: payload.localModels
      };

      // 调用现有的设备注册服务
      const result = await this.nodeService.registerDevice(deviceRegisterRequest);

      return {
        success: true,
        nodeId: result.node_id,
        status: result.status,
        deviceType: result.device_type,
        rewardAddress: result.reward_address
      };
    } catch (error) {
      this.logger.error(`Device registration processing failed: ${error}`);
      return {
        success: false,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * 发送成功响应
   */
  private async sendSuccessResponse(
    to: string,
    nodeId: string,
    status: string
  ): Promise<void> {
    const responseMessage: TunnelMessage = {
      from: this.peerId,
      to,
      type: 'p2p_device_register_response',
      payload: {
        success: true,
        nodeId,
        status,
        timestamp: Date.now(),
        signature: this.generateResponseSignature({
          success: true,
          nodeId,
          status,
          timestamp: Date.now()
        })
      }
    };

    await this.tunnel.handleMessage(responseMessage);
  }

  /**
   * 发送错误响应
   */
  private async sendErrorResponse(to: string, error: string): Promise<void> {
    const responseMessage: TunnelMessage = {
      from: this.peerId,
      to,
      type: 'p2p_device_register_response',
      payload: {
        success: false,
        error,
        timestamp: Date.now(),
        signature: this.generateResponseSignature({
          success: false,
          error,
          timestamp: Date.now()
        })
      }
    };

    await this.tunnel.handleMessage(responseMessage);
  }

  /**
   * 生成响应签名
   */
  private generateResponseSignature(data: any): string {
    // 这里应该使用Gateway的私钥对响应进行签名
    // 暂时返回一个占位符
    return Buffer.from(JSON.stringify(data)).toString('base64');
  }

  /**
   * 解码Base58字符串
   */
  private decodeBase58(base58String: string): Uint8Array {
    const bs58 = require('bs58');
    return bs58.decode(base58String);
  }
}

/**
 * 注册结果接口
 */
interface RegistrationResult {
  success: boolean;
  nodeId?: string;
  status?: string;
  deviceType?: string;
  rewardAddress?: string;
  error?: string;
}
