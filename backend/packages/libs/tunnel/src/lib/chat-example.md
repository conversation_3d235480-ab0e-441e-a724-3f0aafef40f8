# Chat 消息处理系统使用示例

## 概述

这个文档展示了如何使用新实现的 chat 消息处理系统，该系统集成了 TunnelService 和 TaskExecutionService。

## 系统架构

```
TaskExecutionService -> TunnelService -> MessageHandlers -> Node
                                    <-                   <-
```

## 消息流程

### 1. 任务创建和执行

```typescript
// 在 TaskManager 中创建任务
const task = await taskManager.createTask({
  device_id: 'device-uuid',
  model: 'llama2',
  user_id: 'user-uuid'
});

// TaskExecutionService 自动执行以下步骤：
// 1. 查找合适的节点
// 2. 更新任务状态为 'running'
// 3. 发送 chat 请求到节点
```

### 2. Chat 请求消息

系统支持两种类型的 chat 请求：

#### 非流式请求 (chat_request_no_stream)
```typescript
{
  type: 'chat_request_no_stream',
  from: 'gateway-peer-id',
  to: 'node-peer-id',
  payload: {
    taskId: 'task-uuid',
    data: {
      model: 'llama2',
      messages: [
        { role: 'user', content: 'Hello, how are you?' }
      ],
      stream: false
    }
  }
}
```

#### 流式请求 (chat_request_stream)
```typescript
{
  type: 'chat_request_stream',
  from: 'gateway-peer-id',
  to: 'node-peer-id',
  payload: {
    taskId: 'task-uuid',
    data: {
      model: 'llama2',
      messages: [
        { role: 'user', content: 'Hello, how are you?' }
      ],
      stream: true
    }
  }
}
```

### 3. Chat 响应消息

#### 非流式响应 (chat_response_no_stream)
```typescript
{
  type: 'chat_response_no_stream',
  from: 'node-peer-id',
  to: 'gateway-peer-id',
  payload: {
    taskId: 'task-uuid',
    data: {
      id: 'response-id',
      object: 'chat.completion',
      created: 1234567890,
      model: 'llama2',
      system_fingerprint: 'fp_123',
      choices: [{
        index: 0,
        message: {
          role: 'assistant',
          content: 'Hello! I am doing well, thank you for asking.'
        },
        finish_reason: 'stop'
      }],
      usage: {
        prompt_tokens: 10,
        completion_tokens: 15,
        total_tokens: 25
      }
    }
  }
}
```

#### 流式响应 (chat_response_stream)
```typescript
{
  type: 'chat_response_stream',
  from: 'node-peer-id',
  to: 'gateway-peer-id',
  payload: {
    taskId: 'task-uuid',
    data: {
      model: 'llama2',
      created_at: '2024-01-01T00:00:00Z',
      message: {
        role: 'assistant',
        content: 'Hello!'
      },
      done: false, // true for the final message
      total_duration: 1000,
      load_duration: 100,
      prompt_eval_count: 10,
      prompt_eval_duration: 200,
      eval_count: 5,
      eval_duration: 300
    }
  }
}
```

#### 错误响应 (chat_error)
```typescript
{
  type: 'chat_error',
  from: 'node-peer-id',
  to: 'gateway-peer-id',
  payload: {
    taskId: 'task-uuid',
    error: {
      message: 'Model not found',
      type: 'invalid_request_error',
      code: 'model_not_found'
    }
  }
}
```

## 消息处理器

### Outcome 处理器 (发送消息)

- `OutcomeChatRequestNoStreamMessageHandler`: 处理非流式 chat 请求的发送
- `OutcomeChatRequestStreamMessageHandler`: 处理流式 chat 请求的发送

### Income 处理器 (接收消息)

- `IncomeChatResponseNoStreamMessageHandler`: 处理非流式 chat 响应，完成任务
- `IncomeChatResponseStreamMessageHandler`: 处理流式 chat 响应，在最后一个消息时完成任务
- `IncomeChatErrorMessageHandler`: 处理 chat 错误，标记任务失败

## 任务状态管理

1. **pending**: 任务创建后的初始状态
2. **running**: 任务开始执行，chat 请求已发送
3. **completed**: 收到成功响应，任务完成，记录收益
4. **failed**: 收到错误响应或处理失败

## 事件监听器

可以为任务执行添加事件监听器：

```typescript
const listeners: TaskEventListeners = {
  onData: (chunk) => {
    console.log('Received stream chunk:', chunk);
  },
  onComplete: (response) => {
    console.log('Task completed:', response);
  },
  onError: (error) => {
    console.error('Task failed:', error);
  }
};

await taskExecutionService.executeTask(task, listeners);
```

## 自动注册

所有消息处理器都通过 `@MessageHandler` 装饰器自动注册到 `MessageHandlerRegistry` 中，无需手动配置。

## 扩展性

要添加新的消息类型：

1. 在 `tunnel-message.schema.ts` 中定义新的消息 schema
2. 创建对应的 income 和 outcome 处理器
3. 在 `tunnel.module.ts` 中注册新的处理器
4. 更新 `TunnelMessageSchema` 的联合类型

这个系统提供了完整的、可扩展的节点间 chat 通信解决方案。
