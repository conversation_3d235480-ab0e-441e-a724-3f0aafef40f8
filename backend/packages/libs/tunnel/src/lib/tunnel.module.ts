import { Module, Global } from '@nestjs/common';
import { TunnelServiceProvider } from './tunnel.service';
import {MessageHandlerRegistry} from "./message-handler/message-handler.registry";
import {ResponseAdapterServiceProvider} from "./response-adapter.service";
import {IncomePingMessageHandler} from "./message-handler/income/income-ping-message.handler";
import {OutcomePingMessageHandler} from "./message-handler/outcome/outcome-ping-message.handler";
import {MessageGatewayProvider, MessageGatewayServiceImpl} from "./message-gateway/message-gateway.service";
import {DiscoveryModule} from "@nestjs/core";
import {env} from "../env";
import { TaskManagerModule } from '@saito/task-manager';
import {
  IncomeContextPingMessageHandler,
  IncomeContextPongMessageHandler,
  IncomePongMessageHandler,
  IncomeChatResponseStreamMessageHandler,
  IncomeChatResponseNoStreamMessageHandler,
  IncomeCompletionResponseStreamMessageHandler,
  IncomeCompletionResponseNoStreamMessageHandler,
  OutcomeContextPingMessageHandler,
  OutcomeContextPongMessageHandler,
  OutcomePongMessageHandler,
  OutcomeChatRequestStreamMessageHandler,
  OutcomeChatRequestNoStreamMessageHandler,
  OutcomeCompletionRequestStreamMessageHandler,
  OutcomeCompletionRequestNoStreamMessageHandler
} from "./message-handler";

@Global()
@Module({
  imports: [DiscoveryModule, TaskManagerModule],
  providers: [TunnelServiceProvider, MessageGatewayProvider, MessageHandlerRegistry, MessageGatewayServiceImpl, ResponseAdapterServiceProvider,
  // Incoming Handlers
    IncomePingMessageHandler,
    IncomePongMessageHandler,
    IncomeContextPingMessageHandler,
    IncomeContextPongMessageHandler,
    IncomeChatResponseStreamMessageHandler,
    IncomeChatResponseNoStreamMessageHandler,
    IncomeCompletionResponseStreamMessageHandler,
    IncomeCompletionResponseNoStreamMessageHandler,
    // Outcoming Handlers
    OutcomePingMessageHandler,
    OutcomePongMessageHandler,
    OutcomeContextPingMessageHandler,
    OutcomeContextPongMessageHandler,
    OutcomeChatRequestStreamMessageHandler,
    OutcomeChatRequestNoStreamMessageHandler,
    OutcomeCompletionRequestStreamMessageHandler,
    OutcomeCompletionRequestNoStreamMessageHandler,
    {
      provide: 'PEER_ID',
      useValue: env().PEER_ID
    }
  ],
  exports: [
    TunnelServiceProvider,
    MessageHandlerRegistry,
    ResponseAdapterServiceProvider,
    {
      provide: 'PEER_ID',
      useValue: env().PEER_ID
    }
  ],
})
export class TunnelModule {}
