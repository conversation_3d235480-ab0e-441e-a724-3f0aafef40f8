import { Injectable, Logger, Inject, HttpException, HttpStatus, OnModuleInit } from '@nestjs/common';
import { Response, Request } from 'express';
import { OpenAIService as IOpenAIService } from './openai.interface';
import {
  OpenAIChatCompletionRequest,
  OpenAICompletionRequest,
  InferenceError
} from '@saito/models';
import { ResponseAdapterService, StreamResponseHandler } from '@saito/tunnel';
import { OpenAIRequestHandler } from './handlers/openai-request.handler';
import { OpenAIMessageBuilder } from './handlers/openai-message.builder';
import { OpenAIResponseManager } from './handlers/openai-response.manager';
import { OpenAIDataProcessor } from './handlers/openai-data.processor';
import { OpenAIErrorHandler } from './handlers/openai-error.handler';

/**
 * OpenAI 服务实现 - 重构后的简化版本
 * 职责：协调各个处理器组件，实现接口
 * 设计原则：
 * - 单一职责：只负责组件协调
 * - 依赖倒置：依赖于抽象的处理器组件
 * - 接口隔离：只实现必要的 OpenAI 功能
 */
@Injectable()
export class OpenAIServiceImpl implements IOpenAIService, StreamResponseHandler, OnModuleInit {
  private readonly logger = new Logger(OpenAIServiceImpl.name);

  constructor(
    @Inject('ResponseAdapterService') private readonly responseAdapter: ResponseAdapterService,
    private readonly requestHandler: OpenAIRequestHandler,
    private readonly messageBuilder: OpenAIMessageBuilder,
    private readonly responseManager: OpenAIResponseManager,
    private readonly dataProcessor: OpenAIDataProcessor,
    private readonly errorHandler: OpenAIErrorHandler
  ) {}

  /**
   * 模块初始化时注册自己到响应适配器
   */
  onModuleInit() {
    this.responseAdapter.registerServiceHandler('openai', this);
    this.logger.log('[OpenAIService] Registered as stream response handler');
  }

  /**
   * 处理聊天完成请求 - 委托给请求处理器
   */
  async handleChatCompletion(_path: string, body: OpenAIChatCompletionRequest, res: Response): Promise<void> {
    await this.requestHandler.handleChatCompletion(
      _path,
      body,
      res,
      this.messageBuilder,
      this.responseManager,
      this.errorHandler
    );
  }

  /**
   * 处理文本完成请求 - 委托给请求处理器
   */
  async handleCompletion(_path: string, body: OpenAICompletionRequest, res: Response): Promise<void> {
    await this.requestHandler.handleCompletion(
      _path,
      body,
      res,
      this.messageBuilder,
      this.responseManager,
      this.errorHandler
    );
  }

  /**
   * 代理请求 - 暂未实现
   */
  async proxyRequest(_req: Request): Promise<never> {
    const errorResponse = this.errorHandler.createNotImplementedError('Proxy request not implemented');
    throw new HttpException(errorResponse, HttpStatus.NOT_IMPLEMENTED);
  }

  // StreamResponseHandler 接口实现 - 委托给数据处理器

  /**
   * 处理来自设备的流式响应 - 委托给数据处理器
   */
  async handleStreamResponse(taskId: string, payload: unknown): Promise<void> {
    await this.dataProcessor.handleStreamResponse(taskId, payload);
  }

  /**
   * 处理来自设备的非流式响应 - 委托给数据处理器
   */
  async handleNonStreamResponse(taskId: string, payload: unknown): Promise<void> {
    await this.dataProcessor.handleNonStreamResponse(taskId, payload);
  }

  /**
   * 处理来自设备的流式 Completion 响应 - 委托给数据处理器
   */
  async handleCompletionStreamResponse(taskId: string, payload: unknown): Promise<void> {
    await this.dataProcessor.handleCompletionStreamResponse(taskId, payload);
  }

  /**
   * 处理来自设备的非流式 Completion 响应 - 委托给数据处理器
   */
  async handleCompletionNonStreamResponse(taskId: string, payload: unknown): Promise<void> {
    await this.dataProcessor.handleCompletionNonStreamResponse(taskId, payload);
  }

  /**
   * 处理错误响应 - 委托给错误处理器
   */
  async handleErrorResponse(taskId: string, error: InferenceError): Promise<void> {
    await this.errorHandler.handleErrorResponse(taskId, error);
  }

  /**
   * 处理嵌入请求 - 暂未实现
   */
  async handleEmbeddings(_path: string, _body: unknown, res: Response): Promise<void> {
    const errorResponse = this.errorHandler.createNotImplementedError('Embeddings not implemented');
    res.status(HttpStatus.NOT_IMPLEMENTED).json(errorResponse);
  }

  /**
   * 处理模型列表请求 - 暂未实现
   */
  async handleModels(_path: string, res: Response): Promise<void> {
    const errorResponse = this.errorHandler.createNotImplementedError('Models list not implemented');
    res.status(HttpStatus.NOT_IMPLEMENTED).json(errorResponse);
  }
}

export const OpenAIServiceProvider = {
  provide: 'OpenAIService',
  useClass: OpenAIServiceImpl,
};
