import { Module } from '@nestjs/common';
import { OpenAIServiceProvider } from './openai.service';
import { OpenAIRequestHandler } from './handlers/openai-request.handler';
import { OpenAIMessageBuilder } from './handlers/openai-message.builder';
import { OpenAIResponseManager } from './handlers/openai-response.manager';
import { OpenAIDataProcessor } from './handlers/openai-data.processor';
import { OpenAIErrorHandler } from './handlers/openai-error.handler';
import { TunnelModule } from '@saito/tunnel';
import { TaskManagerModule } from '@saito/task-manager';
import { NodeModule } from '@saito/node';

/**
 * OpenAI 模块 - 重构后的版本
 * 职责：提供 OpenAI API 服务的依赖注入配置
 * 设计原则：模块化设计，清晰的依赖关系，单一职责原则
 */
@Module({
  imports: [
    TunnelModule,
    TaskManagerModule,
    NodeModule
  ],
  providers: [
    OpenAIServiceProvider,
    OpenAIRequestHandler,
    OpenAIMessageBuilder,
    OpenAIResponseManager,
    OpenAIDataProcessor,
    OpenAIErrorHandler
  ],
  exports: [
    OpenAIServiceProvider
  ],
})
export class OpenAIModule {}
