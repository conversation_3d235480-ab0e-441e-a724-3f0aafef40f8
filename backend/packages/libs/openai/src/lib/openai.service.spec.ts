import { Test, TestingModule } from '@nestjs/testing';
import { OpenAIServiceImpl } from './openai.service';
import { TunnelService, ResponseAdapterService } from '@saito/tunnel';
import { TaskManager } from '@saito/task-manager';
import { NodeService } from '@saito/node';

describe('OpenAIServiceImpl', () => {
  let service: OpenAIServiceImpl;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        OpenAIServiceImpl,
        {
          provide: 'TunnelService',
          useValue: {
            handleMessage: jest.fn(),
            sendMessage: jest.fn(),
          },
        },
        {
          provide: 'TaskManager',
          useValue: {
            createTask: jest.fn(),
          },
        },
        {
          provide: NodeService,
          useValue: {
            findAvailableNodes: jest.fn(),
          },
        },
        {
          provide: 'PEER_ID',
          useValue: 'test-peer-id',
        },
        {
          provide: 'ResponseAdapterService',
          useValue: {
            registerTaskService: jest.fn(),
            handleStreamResponse: jest.fn(),
            handleErrorResponse: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<OpenAIServiceImpl>(OpenAIServiceImpl);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should have correct dependencies injected', () => {
    expect(service).toBeInstanceOf(OpenAIServiceImpl);
  });
});
