import { Module } from '@nestjs/common';
import { APP_PIPE } from '@nestjs/core';
import { ZodValidationPipe } from 'nestjs-zod';
import { IndexController } from './controllers/index.controller';
import { AuthController } from "./controllers/auth.controller";
import { NodeController } from "./controllers/node.controller";
import { EarningsController } from "./controllers/earnings.controller";
import { ApiKeyController } from "./controllers/apikey.controller";
import { OllamaChatController } from "./controllers/ollama.controller";
import { OpenAIController } from "./controllers/openai.controller";


import { AuthModule } from "@saito/auth";
import { NodeModule } from "@saito/node";
import { EarningsModule } from "@saito/earnings";
import { ApiKeyModule } from "@saito/apikey";
import { TunnelModule } from "@saito/tunnel";
import { TaskManagerModule } from "@saito/task-manager";
import { OpenAIModule } from "@saito/openai";
import { OllamaModule } from "@saito/ollama";
import { ScheduleModule } from '@nestjs/schedule';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    AuthModule,
    NodeModule,
    EarningsModule,
    ApiKeyModule,
    TunnelModule,
    TaskManagerModule,
    OpenAIModule,
    OllamaModule,
  ],
  controllers: [
    IndexController,
    AuthController,
    NodeController,
    EarningsController,
    ApiKeyController,
    OllamaChatController,
    OpenAIController
  ],
  providers: [
    {
      provide: APP_PIPE,
      useClass: ZodValidationPipe,
    }
  ],
})
export class AppModule {}
