import { Request } from 'express';
import { z } from 'zod';

// 定义 JWT 用户信息的 Schema
export const JwtUserSchema = z.object({
  walletAddress: z.string(),
  userId: z.string(),
  sub: z.string()
});

export type JwtUser = z.infer<typeof JwtUserSchema>;

// 扩展 Express 的 Request 接口，添加 user 属性
export interface AuthenticatedRequest extends Request {
  user: JwtUser;
}

// 定义 API 错误的 Schema
export const ApiErrorSchema = z.object({
  message: z.string(),
  status: z.number().optional(),
  code: z.string().optional(),
  details: z.any().optional()
});

export type ApiError = z.infer<typeof ApiErrorSchema>;

// 定义 API 状态的 Schema
export const ApiKeyStatusSchema = z.enum(['active', 'inactive', 'revoked']);

export type ApiKeyStatus = z.infer<typeof ApiKeyStatusSchema>;
