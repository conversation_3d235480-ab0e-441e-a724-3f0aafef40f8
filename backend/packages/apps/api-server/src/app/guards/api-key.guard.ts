import { Injectable, CanActivate, ExecutionContext, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { Observable } from 'rxjs';
import { ApiKeyService } from '@saito/apikey';
import { ApiKeyInfo } from '@saito/models';
import { Request, Response } from 'express';
import * as CryptoJS from 'crypto-js';

// 导入类型声明文件
import type { ExtendedRequest, ExtendedResponse } from './express';

@Injectable()
export class ApiKeyGuard implements CanActivate {
  private readonly logger = new Logger(ApiKeyGuard.name);

  constructor(private readonly apiKeyService: ApiKeyService) {}

  canActivate(
    context: ExecutionContext,
  ): boolean | Promise<boolean> | Observable<boolean> {
    return this.validateRequest(context);
  }

  private async validateRequest(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();

    try {
      // Get the API key from the Authorization header
      const authHeader = request.headers.authorization;
      if (!authHeader) {
        throw new HttpException({
          error: {
            message: 'No API key provided. You can find your API key in your account settings.',
            type: 'invalid_request_error',
            param: null,
            code: 'missing_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }

      // Extract the API key
      const apiKey = authHeader.startsWith('Bearer ')
        ? authHeader.substring(7)
        : authHeader;

      if (!apiKey) {
        throw new HttpException({
          error: {
            message: 'Invalid API key format. API key should be provided as Bearer token.',
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key_format'
          }
        }, HttpStatus.UNAUTHORIZED);
      }

      // Get the API key prefix
      const keyPrefix = apiKey.split('-')[0] + '-';

      // Get the API categories
      const categories = await this.apiKeyService.getApiCategories({ onlyActive: true });

      // Find the category for this key
      const category = categories.data.find(cat => cat.keyPrefix === keyPrefix);
      if (!category) {
        throw new HttpException({
          error: {
            message: `Incorrect API key provided: ${apiKey}. You can find your API key at https://platform.openai.com/account/api-keys.`,
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }

      // Validate the key format using the pattern
      const pattern = new RegExp(category.keyPattern);
      if (!pattern.test(apiKey)) {
        throw new HttpException({
          error: {
            message: `Incorrect API key provided: ${apiKey}. You can find your API key at https://platform.openai.com/account/api-keys.`,
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }

      // Hash the key for lookup
      const salt = 'sight ai';
      const keyHash = CryptoJS.SHA256(salt + apiKey).toString();

      // Validate the key against the database
      const validationResult = await this.apiKeyService.validateApiKey({
        keyHash,
        keyPrefix
      });

      if (!validationResult.valid) {
        
        throw new HttpException({
          error: {
            message: `Incorrect API key provided: ${apiKey}. You can find your API key at https://platform.openai.com/account/api-keys.`,
            type: 'invalid_request_error',
            param: null,
            code: 'invalid_api_key'
          }
        }, HttpStatus.UNAUTHORIZED);
      }

      if (validationResult.status !== 'active') {
        
        throw new HttpException({
          error: {
            message: `API key is ${validationResult.status}. Please check your API key status in your account settings.`,
            type: 'invalid_request_error',
            param: null,
            code: 'api_key_not_active'
          }
        }, HttpStatus.FORBIDDEN);
      }

      // Attach API key info to the request for use in controllers
      request.apiKeyInfo = {
        keyId: validationResult.keyId,
        userId: validationResult.userId,
        category: category.name,
        provider: category.provider,
        model: request.body?.model || request.query?.model || 'default'
      };

      // Log the API usage
      this.logApiUsage(request, apiKey, category.id);

      return true;
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      } else {
        this.logger.error(`API key validation error: ${error}`);
        throw new HttpException({
          error: {
            message: 'Internal server error during API key validation.',
            type: 'api_error',
            param: null,
            code: 'internal_error'
          }
        }, HttpStatus.INTERNAL_SERVER_ERROR);
      }
    }
  }

  private async logApiUsage(request: ExtendedRequest, apiKey: string, categoryId: string) {
    try {
      // 获取请求信息
      const endpoint = request.originalUrl || '';
      const method = request.method || '';
      const keyId = request.apiKeyInfo?.keyId;
      const userId = request.apiKeyInfo?.userId;
      const model = request.apiKeyInfo?.model || 'default';

      

      if (!keyId) {
        
        return;
      }

      // 记录API使用情况
      const requestSize = this.calculateRequestSize(request);
      

      try {
        await this.apiKeyService.logApiUsage({
          apiKeyId: keyId,
          userId,
          categoryId,
          endpoint,
          method,
          model,
          requestSize,
          // 响应大小和响应时间将在响应完成后更新
          statusCode: 200 // 默认值，实际状态码将在响应完成后更新
        });
        
      } catch (logError) {
        this.logger.error(`[API-KEY-GUARD] Error logging initial API usage: ${logError instanceof Error ? logError.message : String(logError)}`);
        if (logError instanceof Error && logError.stack) {
          this.logger.error(`[API-KEY-GUARD] Stack trace: ${logError.stack}`);
        }
      }

      // 为了记录响应时间和响应大小，我们需要在响应完成后更新记录
      // 这里我们使用响应的 finish 事件
      const response = request.res;
      if (response) {
        const startTime = Date.now();

        response.on('finish', async () => {
          try {
            const responseTime = Date.now() - startTime;
            const statusCode = response.statusCode;
            const responseSize = this.calculateResponseSize(response);

            

            // 更新API使用记录
            await this.apiKeyService.updateApiUsageLog({
              apiKeyId: keyId,
              responseTime,
              statusCode,
              responseSize
            });
            
          } catch (updateError) {
            this.logger.error(`[API-KEY-GUARD] Error updating API usage log: ${updateError instanceof Error ? updateError.message : String(updateError)}`);
            if (updateError instanceof Error && updateError.stack) {
              this.logger.error(`[API-KEY-GUARD] Stack trace: ${updateError.stack}`);
            }
          }
        });
      }
    } catch (error) {
      this.logger.error(`[API-KEY-GUARD] Error in logApiUsage: ${error instanceof Error ? error.message : String(error)}`);
      if (error instanceof Error && error.stack) {
        this.logger.error(`[API-KEY-GUARD] Stack trace: ${error.stack}`);
      }
    }
  }

  /**
   * 计算请求大小（字节）
   */
  private calculateRequestSize(request: ExtendedRequest): number {
    try {
      let size = 0;

      // 计算请求头大小
      if (request.headers) {
        size += JSON.stringify(request.headers).length;
      }

      // 计算请求体大小
      if (request.body) {
        size += JSON.stringify(request.body).length;
      }

      // 计算查询参数大小
      if (request.query) {
        size += JSON.stringify(request.query).length;
      }

      return size;
    } catch (error) {
      this.logger.error(`Error calculating request size: ${error}`);
      return 0;
    }
  }

  /**
   * 计算响应大小（字节）
   */
  private calculateResponseSize(response: ExtendedResponse): number {
    try {
      let size = 0;

      // 计算响应头大小
      if (response.getHeaders) {
        size += JSON.stringify(response.getHeaders()).length;
      }

      // 注意：我们无法直接获取响应体大小，因为响应可能已经发送
      // 可以使用 Content-Length 头作为估计
      const contentLength = response.getHeader ? response.getHeader('content-length') : null;
      if (contentLength) {
        // 处理不同类型的 contentLength
        if (typeof contentLength === 'number') {
          size += contentLength;
        } else if (typeof contentLength === 'string') {
          size += parseInt(contentLength, 10);
        } else if (Array.isArray(contentLength) && contentLength.length > 0) {
          // 如果是数组，取第一个元素
          const firstValue = contentLength[0];
          if (typeof firstValue === 'number') {
            size += firstValue;
          } else if (typeof firstValue === 'string') {
            size += parseInt(firstValue, 10);
          }
        }
      }

      return size;
    } catch (error) {
      this.logger.error(`Error calculating response size: ${error}`);
      return 0;
    }
  }
}
