// 简单的测试脚本来验证消息流向
const WebSocket = require('ws');

// 连接到网关
const ws = new WebSocket('ws://localhost:8718');

ws.on('open', function open() {
  console.log('Connected to gateway');
  
  // 注册为设备
  const registerMessage = {
    type: 'register',
    deviceId: '7eec60d7-61f0-436a-9df6-518d9c7b6278'
  };
  
  console.log('Sending register message:', registerMessage);
  ws.send(JSON.stringify(registerMessage));
});

ws.on('message', function message(data) {
  console.log('Received message:', data.toString());
  
  try {
    const msg = JSON.parse(data.toString());
    
    // 如果收到 chat_request_stream 消息，模拟设备响应
    if (msg.type === 'chat_request_stream') {
      console.log('Received chat_request_stream, sending response...');
      
      // 发送流式响应
      const response = {
        from: '7eec60d7-61f0-436a-9df6-518d9c7b6278',
        to: 'gateway',
        type: 'chat_response_stream',
        payload: {
          taskId: msg.payload.taskId,
          data: {
            model: msg.payload.data.model,
            message: {
              role: 'assistant',
              content: 'Hello from device!'
            },
            done: false
          }
        }
      };
      
      ws.send(JSON.stringify(response));
      
      // 发送完成消息
      setTimeout(() => {
        const doneResponse = {
          from: '7eec60d7-61f0-436a-9df6-518d9c7b6278',
          to: 'gateway',
          type: 'chat_response_stream',
          payload: {
            taskId: msg.payload.taskId,
            data: {
              model: msg.payload.data.model,
              done: true,
              total_duration: 1000,
              load_duration: 100,
              prompt_eval_count: 10,
              prompt_eval_duration: 200,
              eval_count: 20,
              eval_duration: 700
            }
          }
        };
        
        ws.send(JSON.stringify(doneResponse));
        console.log('Sent completion response');
      }, 1000);
    }
  } catch (error) {
    console.error('Error parsing message:', error);
  }
});

ws.on('error', function error(err) {
  console.error('WebSocket error:', err);
});

ws.on('close', function close() {
  console.log('Connection closed');
});

// 保持连接
process.on('SIGINT', () => {
  console.log('Closing connection...');
  ws.close();
  process.exit(0);
});
