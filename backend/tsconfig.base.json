{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "declaration": false, "sourceMap": true, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "esModuleInterop": true, "strict": true, "target": "ES2021", "module": "CommonJS", "lib": ["es2021"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "typeRoots": ["node_modules/@types", "types"], "paths": {"@saito/auth": ["packages/libs/auth/src/index.ts"], "@saito/common": ["packages/libs/common/src/index.ts"], "@saito/configs": ["packages/libs/configs/src/index.ts"], "@saito/models": ["packages/libs/models/src/index.ts"], "@saito/persistent": ["packages/libs/persistent/src/index.ts"], "@saito/node": ["packages/libs/node/src/index.ts"], "@saito/redis": ["packages/libs/redis/src/index.ts"], "@saito/earnings": ["packages/libs/earnings/src/index.ts"], "@saito/apikey": ["packages/libs/apikey/src/index.ts"], "@saito/tunnel": ["packages/libs/tunnel/src/index.ts"], "@saito/task-manager": ["packages/libs/task-manager/src/index.ts"], "@saito/openai": ["packages/libs/openai/src/index.ts"], "@saito/ollama": ["packages/libs/ollama/src/index.ts"], "@saito/did": ["packages/libs/did/src/index.ts"]}}, "exclude": ["node_modules", "tmp"], "ts-node": {"require": ["tsconfig-paths/register"]}}