#!/usr/bin/env bash
set -euo pipefail
__prevEnv__="$(env)"

DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" >/dev/null 2>&1 && pwd)"
export NX_WORKSPACE_ROOT=${DIR}

PATH_add node_modules/.bin
PATH_add tools/bin
PATH_add packages/dev/database/bin

export HASURA_VERSION="2.35.1"
export NATS_VERSION="0.1.1"

export FIXTURE_DIR="${NX_WORKSPACE_ROOT}/tests/fixtures"

export WORKSPACE_NAME="saito"
export DEV_NETWORK_NAME="saito_network"
export HASURA_GRAPHQL_SERVER_PORT="28717"
export HASURA_GRAPHQL_ADMIN_SECRET="9AgJckEMHPRgrasj7Ey8jR"

# export NODE_DATABASE_URL="***************************************************/postgres"
export NODE_DATABASE_URL="postgres://postgres:postgres@127.0.0.1:7543/saito_db"

export ETH_RPC_URL="https://rpc.beta.testnet.l2.quarkchain.io:8545"
export ETHSTORAGE_RPC_URL="https://rpc.beta.testnet.l2.ethstorage.io:9596"

export NATS_URL="nats://localhost:4222"


export PRIVATE_KEY=""
export WALLET_ADDRESS="******************************************"
export FLAT_DIR_ADDRESS="******************************************"
export VERIFICATION_CONTRACT_ADDRESS="******************************************"
export CHAIN_ID=3335
export REQUESTER_ADDRESS="******************************************"

export JWT_SECRET="123123"
export REDIS_DB=0
export REDIS_HOST=localhost
export REDIS_PORT=6379

export API_PORT=8718

if [[ -f .envrc.override ]]; then
  source_env .envrc.override
fi

# export updated ENV of this file
node "${NX_WORKSPACE_ROOT}/tools/bin/get-env" "${__prevEnv__}" "$(env)" >"${NX_WORKSPACE_ROOT}/.env" &
