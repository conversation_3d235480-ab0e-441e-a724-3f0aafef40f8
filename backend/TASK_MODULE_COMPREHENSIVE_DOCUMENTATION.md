# TaskModule 完整重构文档

## 📋 目录

1. [项目概述](#项目概述)
2. [重构背景](#重构背景)
3. [架构设计](#架构设计)
4. [核心组件详解](#核心组件详解)
5. [设计模式应用](#设计模式应用)
6. [API文档](#api文档)
7. [使用指南](#使用指南)
8. [测试策略](#测试策略)
9. [部署配置](#部署配置)
10. [故障排除](#故障排除)
11. [性能优化](#性能优化)
12. [扩展指南](#扩展指南)

---

## 📖 项目概述

### 项目背景
TaskModule是Sight AI Miner Gateway系统的核心模块，负责管理AI任务的完整生命周期，包括任务创建、调度、执行、状态管理和结果处理。

### 重构目标
- **消除代码混乱**：将300+行的巨大服务拆分为职责单一的小组件
- **提高逻辑清晰度**：使用抽象思维设计清晰的分层架构
- **增强可维护性**：每个组件独立，易于测试和修改
- **提升扩展性**：通过组合模式支持新功能扩展

### 技术栈
- **框架**: NestJS + TypeScript
- **数据库**: PostgreSQL + Slonik
- **队列**: Redis
- **消息通信**: 自定义Tunnel协议
- **设计模式**: 工厂模式、策略模式、委托模式、依赖注入

---

## 🔄 重构背景

### 重构前的问题

#### 1. 代码混乱
```typescript
// 问题：巨大的单体服务
class TaskExecutionService {
  // 300+行代码，混合了8种不同职责
  async sendChatRequest() { /* 50+行混合逻辑 */ }
  async executeTask() { /* 40+行 */ }
  async findNodeForTask() { /* 30+行 */ }
  async completeTask() { /* 25+行 */ }
  // ... 更多混合逻辑
}
```

#### 2. 依赖过多
```typescript
// 问题：构造函数依赖过多
constructor(
  taskRepository, taskManager, nodeService,
  tunnelService, persistentService, earningsRepository,
  taskQueueService, databaseSyncService, taskExecutorFactory
) // 9个依赖！
```

#### 3. 职责不清
```typescript
// 问题：方法职责混乱
async executeTask() {
  // 验证 + 路由 + 执行 + 状态更新 + 错误处理
  // 所有逻辑混在一起，难以理解和维护
}
```

#### 4. 模块耦合
```typescript
// 问题：模块间双向依赖
TunnelModule ↔ TaskModule // 循环依赖
```

### 重构原则

#### 1. 抽象思维
- **分层设计**：协调层 → 专门组件层 → 抽象实现层 → 基础设施层
- **职责分离**：每个组件只负责一个明确的职责
- **委托模式**：协调器不处理具体逻辑，只负责委托

#### 2. SOLID原则
- **单一职责原则(SRP)**：每个类只有一个改变的理由
- **开闭原则(OCP)**：对扩展开放，对修改关闭
- **里氏替换原则(LSP)**：子类可以替换父类
- **接口隔离原则(ISP)**：客户端不应依赖不需要的接口
- **依赖倒置原则(DIP)**：依赖抽象，不依赖具体实现

#### 3. 设计模式
- **工厂模式**：TaskExecutorFactory根据任务类型创建执行器
- **策略模式**：不同的执行器、路由策略、状态处理策略
- **委托模式**：协调器委托给专门组件处理
- **组合模式**：通过组合专门组件实现复杂功能

---

## 🏗️ 架构设计

### 整体架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    协调层 (Coordination Layer)                │
├─────────────────────────────────────────────────────────────┤
│  TaskExecutionService (40行)     TaskManagerImpl (40行)      │
│  - 轻量级协调器                    - 业务协调                  │
│  - 委托给专门组件                  - 任务生命周期入口           │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                专门组件层 (Specialized Components Layer)       │
├─────────────────────────────────────────────────────────────┤
│  TaskExecutionOrchestrator (80行)   QueueEventHandler (90行)  │
│  - 执行编排                         - 队列事件处理             │
│  - 阶段化执行流程                   - Push/Pull模式处理        │
│                                                              │
│  TaskStatusManager (120行)         TaskMessageHandler (100行) │
│  - 状态管理                         - 消息处理                │
│  - 收益记录                         - 从Tunnel模块迁移         │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│              抽象实现层 (Abstract Implementation Layer)        │
├─────────────────────────────────────────────────────────────┤
│  TaskLifecycleManagerImpl (80行)    TaskRouterImpl (90行)     │
│  - 任务生命周期管理                  - 节点路由选择            │
│  - CRUD操作                         - 负载均衡                │
│                                                              │
│  TaskExecutionCoordinatorImpl (120行) TaskExecutorFactory     │
│  - 执行协调                          - 工厂模式               │
│  - 重试逻辑                          - 流式/非流式执行器       │
└─────────────────────────────────────────────────────────────┘
                                ↓
┌─────────────────────────────────────────────────────────────┐
│                基础设施层 (Infrastructure Layer)              │
├─────────────────────────────────────────────────────────────┤
│  TaskQueueService (Redis)           TaskRepository (DB)       │
│  - Redis队列操作                    - 数据库操作              │
│  - Push/Pull模式                    - SQL查询                │
│                                                              │
│  TunnelService (通信)               NodeService (节点)        │
│  - 消息传输                         - 节点管理                │
│  - 协议处理                         - 负载监控                │
└─────────────────────────────────────────────────────────────┘
```

### 数据流图

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   Client    │───▶│TaskManager  │───▶│TaskExecution│
│   Request   │    │   Impl      │    │   Service   │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
                                              ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│QueueEvent   │◄───│TaskQueue    │◄───│Store Task   │
│  Handler    │    │  Service    │    │ Listeners   │
└─────────────┘    └─────────────┘    └─────────────┘
        │
        ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│TaskExecution│───▶│TaskRouter   │───▶│   Target    │
│Orchestrator │    │   Impl      │    │    Node     │
└─────────────┘    └─────────────┘    └─────────────┘
        │
        ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│TaskExecutor │───▶│TunnelService│───▶│   Node      │
│  Factory    │    │             │    │ Execution   │
└─────────────┘    └─────────────┘    └─────────────┘
        │
        ▼
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│TaskStatus   │───▶│TaskLifecycle│───▶│  Database   │
│  Manager    │    │  Manager    │    │   Update    │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 依赖关系图

```
TaskManagerImpl
    ↓ 依赖
TaskLifecycleManager + TaskExecutionCoordinator
    ↓ 依赖
TaskExecutionService
    ↓ 依赖
QueueEventHandler + TaskStatusManager + TaskExecutionOrchestrator
    ↓ 依赖
TaskRouter + TaskExecutorFactory + TaskMessageHandler
    ↓ 依赖
TunnelService + NodeService + TaskRepository + TaskQueueService
```

---

## 🔧 核心组件详解

### 1. 协调层 (Coordination Layer)

#### TaskExecutionService
**职责**: 轻量级协调器，委托给专门组件处理具体逻辑

```typescript
@Injectable()
export class TaskExecutionService {
  constructor(
    private readonly taskQueueService: TaskQueueService,
    private readonly queueEventHandler: QueueEventHandler,
    private readonly taskStatusManager: TaskStatusManager,
    private readonly executionOrchestrator: TaskExecutionOrchestrator
  ) {}

  // 调度任务执行 - 委托给QueueEventHandler
  async scheduleTaskExecution(task: Task, listeners?: TaskEventListeners): Promise<void> {
    if (listeners) {
      this.queueEventHandler.storeTaskListeners(task.id, listeners);
    }
    await this.taskQueueService.enqueueTask(task);
  }

  // 完成任务 - 委托给TaskStatusManager
  async completeTask(taskId: string, metrics: TaskMetrics): Promise<void> {
    await this.taskStatusManager.completeTaskWithMetrics(taskId, metrics);
  }
}
```

**特点**:
- 代码量: 40行
- 方法长度: 5-10行
- 职责: 纯协调，不处理具体逻辑
- 依赖: 4个专门组件

#### TaskManagerImpl
**职责**: 业务协调，任务生命周期入口

```typescript
@Injectable()
export class TaskManagerImpl implements TaskManagerInterface {
  constructor(
    @Inject('TaskLifecycleManager') private readonly taskLifecycleManager: TaskLifecycleManager,
    @Inject('TaskExecutionCoordinator') private readonly taskExecutionCoordinator: TaskExecutionCoordinator
  ) {}

  async createTask(taskData: CreateTaskRequest, listeners?: TaskEventListeners): Promise<Task> {
    // 使用TaskLifecycleManager创建任务
    const task = await this.taskLifecycleManager.createTask(taskData);

    // 使用TaskExecutionCoordinator调度执行
    await this.taskExecutionCoordinator.scheduleTask(task, listeners);

    return task;
  }
}
```

**特点**:
- 代码量: 40行
- 职责: 高层业务协调
- 依赖: 2个抽象接口

### 2. 专门组件层 (Specialized Components Layer)

#### TaskExecutionOrchestrator
**职责**: 执行编排器，将复杂执行流程分解为清晰阶段

```typescript
@Injectable()
export class TaskExecutionOrchestrator {
  async orchestrateExecution(task: Task, listeners?: TaskEventListeners): Promise<ExecutionResult> {
    const context = this.createExecutionContext(task, listeners);

    try {
      // 阶段1：验证任务状态
      await this.validateTaskForExecution(context);

      // 阶段2：路由到合适的节点
      await this.routeTaskToNode(context);

      // 阶段3：执行任务
      await this.executeTaskOnNode(context);

      return this.createSuccessResult(context);
    } catch (error) {
      return this.createFailureResult(context, error);
    }
  }
}
```

**核心概念**:
- **执行上下文**: 封装执行过程中的所有状态
- **阶段化执行**: 验证 → 路由 → 执行
- **错误处理**: 统一的错误处理和结果返回

**特点**:
- 代码量: 80行
- 方法长度: 15-25行
- 职责: 执行流程编排

#### QueueEventHandler
**职责**: 队列事件处理器，处理Push/Pull模式的队列事件

```typescript
@Injectable()
export class QueueEventHandler {
  setupQueueListeners(): void {
    const modes = this.taskQueueService.getSupportedModes();

    if (modes.pushMode) {
      this.setupPushModeListeners();
    }

    if (modes.pullMode) {
      this.setupPullModeListeners();
    }
  }

  private async handlePushModeTask(queuedTask: QueuedTask): Promise<void> {
    // Push模式：立即尝试执行
    const result = await this.executionOrchestrator.orchestrateExecution(queuedTask.task);
    await this.handleExecutionResult(result, queuedTask.task.id);
  }

  private async handlePullModeTask(queuedTask: QueuedTask, nodeId: string): Promise<void> {
    // Pull模式：节点已选定，直接执行
    const result = await this.executionOrchestrator.orchestrateExecution(queuedTask.task);
    await this.handleExecutionResult(result, queuedTask.task.id);
  }
}
```

**特点**:
- 代码量: 90行
- 职责: 队列事件处理
- 支持: Push/Pull双模式

#### TaskStatusManager
**职责**: 任务状态管理器，专门处理状态变更和生命周期管理

```typescript
@Injectable()
export class TaskStatusManager {
  async updateTaskStatus(taskId: string, status: TaskStatus): Promise<void> {
    const statusHandler = this.getStatusHandler(status);
    await statusHandler(taskId);
  }

  private getStatusHandler(status: TaskStatus): (taskId: string) => Promise<void> {
    const handlers: Record<TaskStatus, (taskId: string) => Promise<void>> = {
      'pending': (taskId) => this.handlePendingStatus(taskId),
      'running': (taskId) => this.handleRunningStatus(taskId),
      'completed': (taskId) => this.handleCompletedStatus(taskId),
      'failed': (taskId) => this.handleFailedStatus(taskId),
      'cancelled': (taskId) => this.handleCancelledStatus(taskId)
    };
    return handlers[status];
  }

  async completeTaskWithMetrics(taskId: string, metrics: TaskMetrics): Promise<void> {
    await this.persistentService.pgPool.transaction(async (conn) => {
      // 更新任务状态和指标
      await this.taskRepository.completeTask(conn, taskId, metrics);

      // 创建收益记录
      const task = await this.taskRepository.getTask(conn, taskId);
      if (task?.device_id) {
        await this.createEarningsRecord(taskId, task.device_id, metrics);
      }
    });
  }
}
```

**核心特性**:
- **策略模式**: 根据状态类型路由到专门处理方法
- **事务处理**: 确保状态更新和收益记录的原子性
- **定时任务**: 自动检查和处理超时任务

**特点**:
- 代码量: 120行
- 职责: 状态管理 + 收益记录
- 模式: 策略模式

### 3. 抽象实现层 (Abstract Implementation Layer)

#### TaskLifecycleManagerImpl
**职责**: 任务生命周期管理，负责CRUD操作和状态管理

```typescript
@Injectable()
export class TaskLifecycleManagerImpl extends AbstractTaskLifecycleManager {
  async createTask(request: CreateTaskRequest): Promise<Task> {
    this.validateCreateTaskRequest(request);

    return await this.persistentService.pgPool.transaction(async (conn) => {
      return await this.taskRepository.createTask(
        conn,
        request.device_id,
        request.model,
        request.user_id
      );
    });
  }

  async updateTaskStatus(taskId: string, status: TaskStatus): Promise<Task> {
    this.validateTaskId(taskId);
    this.validateTaskStatus(status);

    return await this.persistentService.pgPool.transaction(async (conn) => {
      switch (status) {
        case 'running':
          await this.taskRepository.startTask(conn, taskId);
          break;
        case 'failed':
          await this.taskRepository.failTask(conn, taskId);
          break;
        // ... 其他状态处理
      }
      return await this.taskRepository.getTask(conn, taskId);
    });
  }
}
```

**特点**:
- 代码量: 80行
- 职责: 任务CRUD + 状态管理
- 验证: 完整的参数验证

#### TaskRouterImpl
**职责**: 任务路由器，负责节点选择和负载均衡

```typescript
@Injectable()
export class TaskRouterImpl extends AbstractTaskRouter {
  async findBestNode(task: Task): Promise<ChatNode | null> {
    // 获取支持该模型的所有节点
    const availableNodes = await this.getNodesForModel(task.model);

    // 过滤健康的节点
    const healthyNodes = availableNodes.filter(node =>
      this.validateNodeCapability(node, task)
    );

    // 根据策略选择最佳节点
    return this.selectBestNode(healthyNodes, task);
  }

  async getNodeLoad(nodeId: string): Promise<NodeLoadInfo | null> {
    // 检查缓存
    const cached = this.nodeLoadCache.get(nodeId);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.load;
    }

    // 获取节点负载信息
    const loadInfo = await this.fetchNodeLoadInfo(nodeId);

    // 更新缓存
    this.nodeLoadCache.set(nodeId, {
      load: loadInfo,
      timestamp: Date.now()
    });

    return loadInfo;
  }
}
```

**核心特性**:
- **负载缓存**: 30秒缓存，减少网络开销
- **选择策略**: 支持最少负载、随机、轮询等策略
- **健康检查**: 自动过滤不健康的节点

**特点**:
- 代码量: 90行
- 职责: 节点路由 + 负载均衡
- 策略: 多种节点选择策略

#### TaskExecutionCoordinatorImpl
**职责**: 执行协调器，负责任务调度、重试和取消逻辑

```typescript
@Injectable()
export class TaskExecutionCoordinatorImpl extends AbstractTaskExecutionCoordinator {
  async scheduleTask(task: Task, listeners?: TaskEventListeners): Promise<void> {
    this.validateTaskForExecution(task);

    // 存储监听器
    if (listeners) {
      this.taskListeners.set(task.id, listeners);
    }

    // 创建执行状态
    const executionStatus = this.createExecutionStatus(task.id, 'scheduled');
    this.executionStatuses.set(task.id, executionStatus);

    // 查找合适的节点
    const targetNode = await this.taskRouter.findBestNode(task);
    if (!targetNode) {
      await this.handleScheduleFailure(task.id, 'No suitable node found');
      return;
    }

    // 执行任务
    await this.executeTaskOnNode(task, targetNode, listeners);
  }

  async retryTask(taskId: string, listeners?: TaskEventListeners): Promise<boolean> {
    const executionStatus = this.executionStatuses.get(taskId);
    const retryCount = executionStatus ? executionStatus.retryCount + 1 : 1;

    if (!this.canRetry(retryCount)) {
      return false;
    }

    // 延迟重试（指数退避）
    const delay = this.calculateRetryDelay(retryCount);
    await new Promise(resolve => setTimeout(resolve, delay));

    // 重新调度任务
    const task = await this.taskLifecycleManager.getTask(taskId);
    if (task) {
      await this.scheduleTask(task, listeners);
    }

    return true;
  }
}
```

**核心特性**:
- **重试机制**: 指数退避算法
- **状态跟踪**: 完整的执行状态管理
- **批量操作**: 支持批量任务调度

**特点**:
- 代码量: 120行
- 职责: 执行协调 + 重试逻辑
- 算法: 指数退避重试

---

## 🎨 设计模式应用

### 1. 工厂方法模式 (Factory Method Pattern)

#### TaskExecutorFactory
**应用场景**: 根据任务类型创建不同的执行器

```typescript
@Injectable()
export class TaskExecutorFactory {
  constructor(
    private readonly streamingExecutor: StreamingTaskExecutor,
    private readonly nonStreamingExecutor: NonStreamingTaskExecutor
  ) {
    this.registerExecutors();
  }

  getExecutor(task: Task): TaskExecutor {
    // 根据任务特征选择执行器
    for (const executor of this.executors.values()) {
      if (executor.canExecute(task)) {
        return executor;
      }
    }

    // 默认返回非流式执行器
    return this.nonStreamingExecutor;
  }
}
```

**优势**:
- 消除运行时类型检查
- 编译时确定执行器类型
- 易于扩展新的执行器类型

### 2. 策略模式 (Strategy Pattern)

#### 状态处理策略
**应用场景**: 根据任务状态选择不同的处理策略

```typescript
private getStatusHandler(status: TaskStatus): (taskId: string) => Promise<void> {
  const handlers: Record<TaskStatus, (taskId: string) => Promise<void>> = {
    'pending': (taskId) => this.handlePendingStatus(taskId),
    'running': (taskId) => this.handleRunningStatus(taskId),
    'completed': (taskId) => this.handleCompletedStatus(taskId),
    'failed': (taskId) => this.handleFailedStatus(taskId),
    'cancelled': (taskId) => this.handleCancelledStatus(taskId)
  };
  return handlers[status] || ((taskId) => this.handleUnknownStatus(taskId, status));
}
```

#### 节点选择策略
**应用场景**: 根据配置选择不同的节点选择算法

```typescript
enum NodeSelectionStrategy {
  ROUND_ROBIN = 'round_robin',
  LEAST_LOADED = 'least_loaded',
  RANDOM = 'random',
  PRIORITY = 'priority'
}

protected selectBestNode(nodes: ChatNode[], task: Task): ChatNode | null {
  switch (this.strategy) {
    case NodeSelectionStrategy.LEAST_LOADED:
      return this.selectLeastLoadedNode(nodes);
    case NodeSelectionStrategy.RANDOM:
      return this.selectRandomNode(nodes);
    case NodeSelectionStrategy.ROUND_ROBIN:
      return this.selectRoundRobinNode(nodes);
    default:
      return nodes[0];
  }
}
```

### 3. 委托模式 (Delegation Pattern)

#### 协调器委托
**应用场景**: 协调器不处理具体逻辑，委托给专门组件

```typescript
class TaskExecutionService {
  // 委托给TaskStatusManager处理状态更新
  async completeTask(taskId: string, metrics: TaskMetrics): Promise<void> {
    await this.taskStatusManager.completeTaskWithMetrics(taskId, metrics);
  }

  // 委托给QueueEventHandler处理调度
  async scheduleTaskExecution(task: Task, listeners?: TaskEventListeners): Promise<void> {
    if (listeners) {
      this.queueEventHandler.storeTaskListeners(task.id, listeners);
    }
    await this.taskQueueService.enqueueTask(task);
  }
}
```

**优势**:
- 协调器代码简洁
- 职责分离清晰
- 易于测试和维护

### 4. 模板方法模式 (Template Method Pattern)

#### 抽象执行器
**应用场景**: 定义执行器的通用流程，子类实现具体步骤

```typescript
abstract class AbstractTaskExecutor implements TaskExecutor {
  async execute(task: Task, targetNode: ChatNode, listeners?: TaskEventListeners): Promise<void> {
    // 模板方法定义执行流程
    this.validateTask(task);
    this.validateNode(targetNode);

    const chatData = this.prepareChatData(task);
    const chatMessage = this.createChatMessage(task, targetNode, chatData);
    const responseListener = this.createResponseListener(task, listeners);

    await this.tunnelService.handleMessage(chatMessage, responseListener);
  }

  // 抽象方法，由子类实现
  abstract prepareChatData(task: Task): any;
  abstract createChatMessage(task: Task, targetNode: ChatNode, chatData: any): any;
  abstract createResponseListener(task: Task, listeners?: TaskEventListeners): any;
}
```

### 5. 依赖注入模式 (Dependency Injection Pattern)

#### 接口抽象
**应用场景**: 依赖抽象接口，不依赖具体实现

```typescript
@Injectable()
export class TaskManagerImpl {
  constructor(
    @Inject('TaskLifecycleManager') private readonly taskLifecycleManager: TaskLifecycleManager,
    @Inject('TaskExecutionCoordinator') private readonly taskExecutionCoordinator: TaskExecutionCoordinator
  ) {}
}

// 模块配置
@Module({
  providers: [
    {
      provide: 'TaskLifecycleManager',
      useClass: TaskLifecycleManagerImpl,
    },
    {
      provide: 'TaskExecutionCoordinator',
      useClass: TaskExecutionCoordinatorImpl,
    }
  ]
})
export class TaskManagerModule {}
```

**优势**:
- 松耦合设计
- 易于单元测试
- 支持运行时替换实现

---

## 📚 API文档

### 1. TaskManagerInterface

#### createTask
创建新任务并调度执行

```typescript
async createTask(
  request: CreateTaskRequest,
  listeners?: TaskEventListeners
): Promise<Task>
```

**参数**:
- `request`: 任务创建请求
  - `device_id`: 设备ID
  - `model`: AI模型名称
  - `user_id`: 用户ID
- `listeners`: 可选的事件监听器

**返回**: 创建的任务对象

**示例**:
```typescript
const task = await taskManager.createTask({
  device_id: 'device-123',
  model: 'gpt-4',
  user_id: 'user-456'
}, {
  onProgress: (progress) => console.log('Progress:', progress),
  onComplete: (result) => console.log('Completed:', result),
  onError: (error) => console.error('Error:', error)
});
```

#### getTask
根据ID获取任务

```typescript
async getTask(taskId: string): Promise<Task | null>
```

**参数**:
- `taskId`: 任务ID

**返回**: 任务对象或null

#### updateTaskStatus
更新任务状态

```typescript
async updateTaskStatus(taskId: string, status: TaskStatus): Promise<Task>
```

**参数**:
- `taskId`: 任务ID
- `status`: 新状态 ('pending' | 'running' | 'completed' | 'failed' | 'cancelled')

**返回**: 更新后的任务对象

### 2. TaskExecutionService

#### scheduleTaskExecution
调度任务执行

```typescript
async scheduleTaskExecution(
  task: Task,
  listeners?: TaskEventListeners
): Promise<void>
```

**参数**:
- `task`: 要执行的任务
- `listeners`: 可选的事件监听器

#### completeTask
完成任务并记录指标

```typescript
async completeTask(taskId: string, metrics: TaskMetrics): Promise<void>
```

**参数**:
- `taskId`: 任务ID
- `metrics`: 执行指标
  - `execution_time_ms`: 执行时间(毫秒)
  - `tokens_used`: 使用的token数量
  - `cost`: 执行成本

#### failTask
标记任务失败

```typescript
async failTask(taskId: string, errorMessage?: string): Promise<void>
```

**参数**:
- `taskId`: 任务ID
- `errorMessage`: 可选的错误信息

### 3. TaskEventListeners

事件监听器接口，用于监听任务执行过程中的事件

```typescript
interface TaskEventListeners {
  onProgress?: (progress: TaskProgress) => void;
  onComplete?: (result: TaskResult) => void;
  onError?: (error: TaskError) => void;
  onStatusChange?: (status: TaskStatus) => void;
}
```

**事件类型**:
- `onProgress`: 任务执行进度更新
- `onComplete`: 任务执行完成
- `onError`: 任务执行出错
- `onStatusChange`: 任务状态变更

### 4. 数据类型定义

#### Task
```typescript
interface Task {
  id: string;
  device_id: string;
  model: string;
  user_id: string;
  status: TaskStatus;
  created_at: Date;
  updated_at: Date;
  started_at?: Date;
  completed_at?: Date;
  execution_time_ms?: number;
  tokens_used?: number;
  cost?: number;
  error_message?: string;
}
```

#### TaskMetrics
```typescript
interface TaskMetrics {
  execution_time_ms: number;
  tokens_used: number;
  cost: number;
  additional_data?: Record<string, any>;
}
```

#### ExecutionContext
```typescript
interface ExecutionContext {
  task: Task;
  listeners?: TaskEventListeners;
  startTime: Date;
  phase: ExecutionPhase;
  targetNode: ChatNode | null;
  error: Error | null;
  retryCount: number;
}
```

---

## 📖 使用指南

### 1. 基本使用

#### 创建和执行任务
```typescript
import { TaskManagerInterface } from '@saito/task-manager';

@Injectable()
export class ChatService {
  constructor(
    @Inject('TaskManager') private readonly taskManager: TaskManagerInterface
  ) {}

  async processUserRequest(userId: string, deviceId: string, model: string) {
    // 创建任务
    const task = await this.taskManager.createTask({
      user_id: userId,
      device_id: deviceId,
      model: model
    }, {
      onProgress: (progress) => {
        console.log(`Task ${task.id} progress: ${progress.percentage}%`);
      },
      onComplete: (result) => {
        console.log(`Task ${task.id} completed:`, result);
      },
      onError: (error) => {
        console.error(`Task ${task.id} failed:`, error);
      }
    });

    return task;
  }
}
```

#### 查询任务状态
```typescript
async getTaskStatus(taskId: string) {
  const task = await this.taskManager.getTask(taskId);
  if (!task) {
    throw new Error('Task not found');
  }

  return {
    id: task.id,
    status: task.status,
    progress: this.calculateProgress(task),
    estimatedCompletion: this.estimateCompletion(task)
  };
}
```

### 2. 高级使用

#### 批量任务处理
```typescript
async processBatchTasks(requests: CreateTaskRequest[]) {
  const tasks = await Promise.all(
    requests.map(request =>
      this.taskManager.createTask(request, {
        onComplete: (result) => this.handleBatchTaskComplete(result),
        onError: (error) => this.handleBatchTaskError(error)
      })
    )
  );

  return tasks;
}
```

#### 自定义执行器
```typescript
@Injectable()
export class CustomTaskExecutor extends AbstractTaskExecutor {
  canExecute(task: Task): boolean {
    return task.model.startsWith('custom-');
  }

  protected prepareChatData(task: Task): any {
    return {
      model: task.model,
      custom_parameters: this.getCustomParameters(task)
    };
  }

  protected createChatMessage(task: Task, targetNode: ChatNode, chatData: any): any {
    return {
      type: 'custom_chat_request',
      task_id: task.id,
      node_id: targetNode.node_id,
      data: chatData
    };
  }
}
```

#### 自定义路由策略
```typescript
@Injectable()
export class PriorityBasedRouter extends AbstractTaskRouter {
  protected selectBestNode(nodes: ChatNode[], task: Task): ChatNode | null {
    // 根据任务优先级和节点能力选择
    const priorityNodes = nodes.filter(node =>
      this.getNodePriority(node) >= this.getTaskPriority(task)
    );

    return priorityNodes.length > 0
      ? this.selectLeastLoadedNode(priorityNodes)
      : null;
  }

  private getNodePriority(node: ChatNode): number {
    // 根据节点性能计算优先级
    return node.metrics.cpu_usage_percent < 50 ? 3 :
           node.metrics.cpu_usage_percent < 80 ? 2 : 1;
  }
}
```

### 3. 配置和定制

#### 模块配置
```typescript
@Module({
  imports: [TaskManagerModule],
  providers: [
    // 使用自定义实现
    {
      provide: 'TaskRouter',
      useClass: PriorityBasedRouter,
    },
    // 注册自定义执行器
    CustomTaskExecutor,
  ],
})
export class CustomTaskModule {}
```

#### 环境配置
```typescript
// config/task.config.ts
export const taskConfig = {
  queue: {
    mode: 'hybrid', // 'push' | 'pull' | 'hybrid'
    redis: {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT) || 6379,
    }
  },
  execution: {
    timeout: 300000, // 5分钟
    retryAttempts: 3,
    retryDelay: 1000, // 1秒
  },
  routing: {
    strategy: 'least_loaded', // 'round_robin' | 'least_loaded' | 'random'
    cacheTimeout: 30000, // 30秒
  }
};
```

---

## 🧪 测试策略

### 1. 单元测试

#### 组件隔离测试
每个组件都可以独立测试，使用Mock依赖

```typescript
// task-execution.service.spec.ts
describe('TaskExecutionService', () => {
  let service: TaskExecutionService;
  let mockQueueEventHandler: jest.Mocked<QueueEventHandler>;
  let mockTaskStatusManager: jest.Mocked<TaskStatusManager>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        TaskExecutionService,
        {
          provide: QueueEventHandler,
          useValue: createMockQueueEventHandler(),
        },
        {
          provide: TaskStatusManager,
          useValue: createMockTaskStatusManager(),
        },
      ],
    }).compile();

    service = module.get<TaskExecutionService>(TaskExecutionService);
    mockQueueEventHandler = module.get(QueueEventHandler);
    mockTaskStatusManager = module.get(TaskStatusManager);
  });

  describe('scheduleTaskExecution', () => {
    it('should store listeners and enqueue task', async () => {
      const task = createMockTask();
      const listeners = createMockListeners();

      await service.scheduleTaskExecution(task, listeners);

      expect(mockQueueEventHandler.storeTaskListeners).toHaveBeenCalledWith(task.id, listeners);
      expect(mockTaskQueueService.enqueueTask).toHaveBeenCalledWith(task);
    });
  });
});
```

#### 策略模式测试
```typescript
// task-status-manager.spec.ts
describe('TaskStatusManager', () => {
  describe('updateTaskStatus', () => {
    it('should route to correct handler based on status', async () => {
      const taskId = 'test-task-id';

      // 测试不同状态的路由
      await manager.updateTaskStatus(taskId, 'running');
      expect(manager.handleRunningStatus).toHaveBeenCalledWith(taskId);

      await manager.updateTaskStatus(taskId, 'completed');
      expect(manager.handleCompletedStatus).toHaveBeenCalledWith(taskId);

      await manager.updateTaskStatus(taskId, 'failed');
      expect(manager.handleFailedStatus).toHaveBeenCalledWith(taskId);
    });
  });
});
```

### 2. 集成测试

#### 端到端任务流程测试
```typescript
// task-flow.integration.spec.ts
describe('Task Flow Integration', () => {
  let app: INestApplication;
  let taskManager: TaskManagerInterface;
  let redisClient: Redis;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [TaskManagerModule, TestDatabaseModule, TestRedisModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    taskManager = app.get('TaskManager');
    redisClient = app.get('REDIS_CLIENT');
  });

  it('should complete full task lifecycle', async () => {
    // 1. 创建任务
    const task = await taskManager.createTask({
      device_id: 'test-device',
      model: 'test-model',
      user_id: 'test-user'
    });

    expect(task.status).toBe('pending');

    // 2. 等待任务被调度
    await waitForTaskStatus(task.id, 'running', 5000);

    // 3. 模拟任务完成
    await taskManager.completeTask(task.id, {
      execution_time_ms: 1000,
      tokens_used: 100,
      cost: 0.01
    });

    // 4. 验证最终状态
    const completedTask = await taskManager.getTask(task.id);
    expect(completedTask.status).toBe('completed');
    expect(completedTask.execution_time_ms).toBe(1000);
  });
});
```

### 3. 性能测试

#### 并发任务处理测试
```typescript
// performance.spec.ts
describe('Performance Tests', () => {
  it('should handle 100 concurrent tasks', async () => {
    const startTime = Date.now();
    const tasks = Array.from({ length: 100 }, (_, i) =>
      taskManager.createTask({
        device_id: `device-${i}`,
        model: 'test-model',
        user_id: `user-${i}`
      })
    );

    const results = await Promise.all(tasks);
    const endTime = Date.now();

    expect(results).toHaveLength(100);
    expect(endTime - startTime).toBeLessThan(5000); // 5秒内完成
  });
});
```

### 4. 错误处理测试

#### 故障恢复测试
```typescript
describe('Error Handling', () => {
  it('should retry failed tasks', async () => {
    // 模拟节点故障
    mockNodeService.getTargetNode.mockRejectedValueOnce(new Error('Node unavailable'));
    mockNodeService.getTargetNode.mockResolvedValueOnce(createMockNode());

    const task = await taskManager.createTask(createTaskRequest());

    // 等待重试完成
    await waitForTaskStatus(task.id, 'completed', 10000);

    expect(mockNodeService.getTargetNode).toHaveBeenCalledTimes(2);
  });
});
```

---

## 🚀 部署配置

### 1. Docker配置

#### Dockerfile
```dockerfile
FROM node:18-alpine

WORKDIR /app

# 复制依赖文件
COPY package*.json ./
COPY pnpm-lock.yaml ./

# 安装依赖
RUN npm install -g pnpm
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 暴露端口
EXPOSE 3000

# 启动命令
CMD ["pnpm", "start:prod"]
```

#### docker-compose.yml
```yaml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=****************************************/taskdb
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
    volumes:
      - ./logs:/app/logs

  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: taskdb
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./migrations:/docker-entrypoint-initdb.d

  redis:
    image: redis:7-alpine
    volumes:
      - redis_data:/data

volumes:
  postgres_data:
  redis_data:
```

### 2. 环境配置

#### 生产环境配置
```typescript
// config/production.ts
export const productionConfig = {
  database: {
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT) || 5432,
    username: process.env.DB_USERNAME,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    ssl: true,
    pool: {
      min: 5,
      max: 20,
      acquireTimeoutMillis: 30000,
      idleTimeoutMillis: 30000
    }
  },
  redis: {
    host: process.env.REDIS_HOST,
    port: parseInt(process.env.REDIS_PORT) || 6379,
    password: process.env.REDIS_PASSWORD,
    retryDelayOnFailover: 100,
    maxRetriesPerRequest: 3
  },
  task: {
    execution: {
      timeout: 600000, // 10分钟
      retryAttempts: 5,
      retryDelay: 2000
    },
    queue: {
      mode: 'hybrid',
      batchSize: 10,
      processingInterval: 1000
    }
  },
  logging: {
    level: 'info',
    format: 'json',
    transports: ['file', 'console']
  }
};
```

### 3. 监控配置

#### Prometheus指标
```typescript
// monitoring/metrics.ts
import { register, Counter, Histogram, Gauge } from 'prom-client';

export const taskMetrics = {
  tasksCreated: new Counter({
    name: 'tasks_created_total',
    help: 'Total number of tasks created',
    labelNames: ['model', 'device_type']
  }),

  taskDuration: new Histogram({
    name: 'task_execution_duration_seconds',
    help: 'Task execution duration in seconds',
    labelNames: ['model', 'status'],
    buckets: [0.1, 0.5, 1, 2, 5, 10, 30, 60]
  }),

  activeNodes: new Gauge({
    name: 'active_nodes_count',
    help: 'Number of active nodes',
    labelNames: ['model']
  }),

  queueSize: new Gauge({
    name: 'task_queue_size',
    help: 'Current task queue size',
    labelNames: ['priority']
  })
};
```

---

## 🔧 故障排除

### 1. 常见问题

#### 任务卡在pending状态
**症状**: 任务创建后一直处于pending状态，不被执行

**可能原因**:
1. 没有可用的节点
2. 队列服务异常
3. 网络连接问题

**排查步骤**:
```bash
# 1. 检查节点状态
curl http://localhost:3000/api/nodes/status

# 2. 检查队列状态
redis-cli -h localhost -p 6379
> LLEN task_queue
> LRANGE task_queue 0 -1

# 3. 检查日志
docker logs app_container | grep "task.*pending"
```

**解决方案**:
```typescript
// 手动重新调度任务
await taskManager.rescheduleTask(taskId);

// 或者重启队列处理器
await queueEventHandler.restart();
```

#### 任务执行超时
**症状**: 任务长时间运行后被标记为失败

**可能原因**:
1. 节点响应慢
2. 网络延迟
3. 任务复杂度高

**排查步骤**:
```typescript
// 检查任务执行时间
const task = await taskManager.getTask(taskId);
const executionTime = Date.now() - task.started_at.getTime();
console.log(`Task ${taskId} has been running for ${executionTime}ms`);

// 检查节点负载
const nodeLoad = await taskRouter.getNodeLoad(task.node_id);
console.log('Node load:', nodeLoad);
```

**解决方案**:
```typescript
// 增加超时时间
const extendedConfig = {
  ...taskConfig,
  execution: {
    ...taskConfig.execution,
    timeout: 900000 // 15分钟
  }
};

// 或者使用更强的节点
await taskRouter.setNodePriority(nodeId, 'high');
```

### 2. 性能问题

#### 队列处理缓慢
**症状**: 任务在队列中等待时间过长

**排查步骤**:
```typescript
// 检查队列统计
const queueStats = await taskQueueService.getStats();
console.log('Queue stats:', queueStats);

// 检查处理器状态
const handlerStats = queueEventHandler.getHandlerStats();
console.log('Handler stats:', handlerStats);
```

**优化方案**:
```typescript
// 增加并发处理数
const optimizedConfig = {
  queue: {
    concurrency: 10, // 增加到10个并发
    batchSize: 20,   // 增加批处理大小
    processingInterval: 500 // 减少处理间隔
  }
};

// 启用批量处理
await queueEventHandler.enableBatchProcessing(optimizedConfig);
```

### 3. 调试工具

#### 任务状态追踪
```typescript
// debug/task-tracer.ts
export class TaskTracer {
  private traces = new Map<string, TaskTrace[]>();

  trace(taskId: string, event: string, data?: any): void {
    if (!this.traces.has(taskId)) {
      this.traces.set(taskId, []);
    }

    this.traces.get(taskId).push({
      timestamp: new Date(),
      event,
      data
    });
  }

  getTrace(taskId: string): TaskTrace[] {
    return this.traces.get(taskId) || [];
  }

  exportTrace(taskId: string): string {
    const trace = this.getTrace(taskId);
    return JSON.stringify(trace, null, 2);
  }
}
```

#### 健康检查端点
```typescript
// health/task-health.controller.ts
@Controller('health')
export class TaskHealthController {
  @Get('tasks')
  async checkTaskHealth(): Promise<HealthStatus> {
    const stats = await this.taskExecutionService.getExecutionStats();

    return {
      status: stats.serviceStatus === 'active' ? 'healthy' : 'unhealthy',
      details: {
        queueSize: stats.queueHandler.queueSize,
        activeNodes: stats.queueHandler.activeNodes,
        processingTasks: stats.statusManager.processingTasks
      }
    };
  }
}
```

---

## ⚡ 性能优化

### 1. 数据库优化

#### 索引优化
```sql
-- 任务查询优化
CREATE INDEX CONCURRENTLY idx_tasks_status_created
ON tasks(status, created_at)
WHERE status IN ('pending', 'running');

-- 设备任务查询优化
CREATE INDEX CONCURRENTLY idx_tasks_device_status
ON tasks(device_id, status, created_at);

-- 用户任务查询优化
CREATE INDEX CONCURRENTLY idx_tasks_user_created
ON tasks(user_id, created_at DESC);

-- 收益查询优化
CREATE INDEX CONCURRENTLY idx_earnings_device_date
ON earnings(device_id, created_at);
```

#### 连接池优化
```typescript
// config/database.ts
export const databaseConfig = {
  pool: {
    min: 5,           // 最小连接数
    max: 20,          // 最大连接数
    acquireTimeoutMillis: 30000,  // 获取连接超时
    idleTimeoutMillis: 30000,     // 空闲连接超时
    reapIntervalMillis: 1000,     // 清理间隔
    createTimeoutMillis: 3000,    // 创建连接超时
    destroyTimeoutMillis: 5000,   // 销毁连接超时
    createRetryIntervalMillis: 200, // 重试间隔
  },
  // 启用预编译语句
  preparedStatements: true,
  // 启用查询缓存
  queryCache: {
    enabled: true,
    maxSize: 1000,
    ttl: 300000 // 5分钟
  }
};
```

### 2. Redis优化

#### 连接优化
```typescript
// config/redis.ts
export const redisConfig = {
  // 连接池配置
  pool: {
    min: 5,
    max: 20
  },
  // 重试配置
  retryDelayOnFailover: 100,
  maxRetriesPerRequest: 3,
  // 启用管道
  enableAutoPipelining: true,
  // 压缩配置
  compression: 'gzip',
  // 键过期策略
  keyPrefix: 'task:',
  // 集群配置（如果使用Redis集群）
  cluster: {
    enableReadyCheck: false,
    redisOptions: {
      password: process.env.REDIS_PASSWORD
    }
  }
};
```

#### 队列优化
```typescript
// queue/optimized-queue.service.ts
@Injectable()
export class OptimizedTaskQueueService extends TaskQueueService {
  // 批量入队
  async enqueueBatch(tasks: Task[]): Promise<void> {
    const pipeline = this.redis.pipeline();

    for (const task of tasks) {
      pipeline.lpush(this.getQueueKey(task.model), JSON.stringify(task));
    }

    await pipeline.exec();
  }

  // 批量出队
  async dequeueBatch(models: string[], batchSize: number = 10): Promise<QueuedTask[]> {
    const pipeline = this.redis.pipeline();

    for (const model of models) {
      for (let i = 0; i < batchSize; i++) {
        pipeline.rpop(this.getQueueKey(model));
      }
    }

    const results = await pipeline.exec();
    return results
      .filter(([err, result]) => !err && result)
      .map(([, result]) => JSON.parse(result as string));
  }
}
```

### 3. 内存优化

#### 缓存策略
```typescript
// cache/task-cache.service.ts
@Injectable()
export class TaskCacheService {
  private readonly taskCache = new LRUCache<string, Task>({
    max: 10000,        // 最大缓存数量
    ttl: 300000,       // 5分钟TTL
    updateAgeOnGet: true,
    allowStale: false
  });

  private readonly nodeCache = new LRUCache<string, ChatNode>({
    max: 1000,
    ttl: 30000,        // 30秒TTL
    updateAgeOnGet: true
  });

  async getTask(taskId: string): Promise<Task | null> {
    // 先从缓存获取
    let task = this.taskCache.get(taskId);
    if (task) {
      return task;
    }

    // 缓存未命中，从数据库获取
    task = await this.taskRepository.getTask(taskId);
    if (task) {
      this.taskCache.set(taskId, task);
    }

    return task;
  }

  invalidateTask(taskId: string): void {
    this.taskCache.delete(taskId);
  }
}
```

#### 内存监控
```typescript
// monitoring/memory-monitor.ts
@Injectable()
export class MemoryMonitor {
  @Cron('*/30 * * * * *') // 每30秒检查一次
  checkMemoryUsage(): void {
    const usage = process.memoryUsage();
    const usageInMB = {
      rss: Math.round(usage.rss / 1024 / 1024),
      heapTotal: Math.round(usage.heapTotal / 1024 / 1024),
      heapUsed: Math.round(usage.heapUsed / 1024 / 1024),
      external: Math.round(usage.external / 1024 / 1024)
    };

    // 内存使用率超过80%时告警
    if (usageInMB.heapUsed / usageInMB.heapTotal > 0.8) {
      this.logger.warn('High memory usage detected', usageInMB);
      this.triggerGarbageCollection();
    }
  }

  private triggerGarbageCollection(): void {
    if (global.gc) {
      global.gc();
      this.logger.log('Garbage collection triggered');
    }
  }
}
```

### 4. 并发优化

#### 任务并发控制
```typescript
// execution/concurrent-executor.ts
@Injectable()
export class ConcurrentTaskExecutor {
  private readonly semaphore: Semaphore;

  constructor() {
    // 限制并发执行数量
    this.semaphore = new Semaphore(10);
  }

  async executeTask(task: Task, listeners?: TaskEventListeners): Promise<void> {
    // 获取信号量
    await this.semaphore.acquire();

    try {
      await this.doExecuteTask(task, listeners);
    } finally {
      // 释放信号量
      this.semaphore.release();
    }
  }

  private async doExecuteTask(task: Task, listeners?: TaskEventListeners): Promise<void> {
    // 实际执行逻辑
    const executor = this.taskExecutorFactory.getExecutor(task);
    await executor.execute(task, listeners);
  }
}
```

---

## 🔧 扩展指南

### 1. 添加新的执行器

#### 步骤1：创建执行器类
```typescript
// executors/custom-model-executor.ts
@Injectable()
export class CustomModelExecutor extends AbstractTaskExecutor {
  canExecute(task: Task): boolean {
    return task.model.startsWith('custom-model-');
  }

  protected prepareChatData(task: Task): CustomChatData {
    return {
      model: task.model,
      parameters: this.extractCustomParameters(task),
      optimization: this.getOptimizationSettings(task)
    };
  }

  protected createChatMessage(task: Task, targetNode: ChatNode, chatData: CustomChatData): ChatMessage {
    return {
      type: 'custom_model_request',
      task_id: task.id,
      node_id: targetNode.node_id,
      data: chatData,
      timeout: this.getCustomTimeout(task)
    };
  }

  protected createResponseListener(task: Task, listeners?: TaskEventListeners): ResponseListener {
    return {
      onData: (data) => this.handleCustomResponse(task, data, listeners),
      onError: (error) => this.handleCustomError(task, error, listeners),
      onComplete: () => this.handleCustomComplete(task, listeners)
    };
  }
}
```

#### 步骤2：注册执行器
```typescript
// task-manager.module.ts
@Module({
  providers: [
    // 注册新执行器
    CustomModelExecutor,

    // 更新工厂
    {
      provide: TaskExecutorFactory,
      useFactory: (
        streamingExecutor: StreamingTaskExecutor,
        nonStreamingExecutor: NonStreamingTaskExecutor,
        customExecutor: CustomModelExecutor
      ) => {
        const factory = new TaskExecutorFactory(streamingExecutor, nonStreamingExecutor);
        factory.registerExecutor(customExecutor);
        return factory;
      },
      inject: [StreamingTaskExecutor, NonStreamingTaskExecutor, CustomModelExecutor]
    }
  ]
})
export class TaskManagerModule {}
```

### 2. 添加新的路由策略

#### 步骤1：实现路由策略
```typescript
// routers/geographic-router.ts
@Injectable()
export class GeographicRouter extends AbstractTaskRouter {
  protected selectBestNode(nodes: ChatNode[], task: Task): ChatNode | null {
    // 根据地理位置选择最近的节点
    const userLocation = this.getUserLocation(task.user_id);
    if (!userLocation) {
      return this.selectRandomNode(nodes);
    }

    // 计算距离并选择最近的节点
    const nodesWithDistance = nodes.map(node => ({
      node,
      distance: this.calculateDistance(userLocation, node.location)
    }));

    nodesWithDistance.sort((a, b) => a.distance - b.distance);
    return nodesWithDistance[0]?.node || null;
  }

  private calculateDistance(loc1: Location, loc2: Location): number {
    // 使用Haversine公式计算距离
    const R = 6371; // 地球半径（公里）
    const dLat = this.toRad(loc2.lat - loc1.lat);
    const dLon = this.toRad(loc2.lon - loc1.lon);

    const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
              Math.cos(this.toRad(loc1.lat)) * Math.cos(this.toRad(loc2.lat)) *
              Math.sin(dLon/2) * Math.sin(dLon/2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }
}
```

#### 步骤2：配置路由策略
```typescript
// 在模块中注册
{
  provide: 'TaskRouter',
  useClass: GeographicRouter,
}
```

### 3. 添加新的状态处理器

#### 自定义状态
```typescript
// 扩展TaskStatus类型
export type ExtendedTaskStatus = TaskStatus | 'paused' | 'scheduled' | 'retrying';

// 实现状态处理器
@Injectable()
export class ExtendedTaskStatusManager extends TaskStatusManager {
  protected getStatusHandler(status: ExtendedTaskStatus): (taskId: string) => Promise<void> {
    const baseHandlers = super.getStatusHandler(status as TaskStatus);

    const extendedHandlers: Record<string, (taskId: string) => Promise<void>> = {
      'paused': (taskId) => this.handlePausedStatus(taskId),
      'scheduled': (taskId) => this.handleScheduledStatus(taskId),
      'retrying': (taskId) => this.handleRetryingStatus(taskId)
    };

    return extendedHandlers[status] || baseHandlers;
  }

  private async handlePausedStatus(taskId: string): Promise<void> {
    // 暂停任务的处理逻辑
    await this.taskRepository.updateTaskStatus(taskId, 'paused');
    await this.taskQueueService.removeFromQueue(taskId);
  }
}
```

### 4. 添加新的监听器类型

#### 自定义事件监听器
```typescript
// interfaces/extended-listeners.ts
export interface ExtendedTaskEventListeners extends TaskEventListeners {
  onPause?: (taskId: string) => void;
  onResume?: (taskId: string) => void;
  onRetry?: (taskId: string, attempt: number) => void;
  onMetricsUpdate?: (taskId: string, metrics: Partial<TaskMetrics>) => void;
}

// 实现扩展的事件处理
@Injectable()
export class ExtendedQueueEventHandler extends QueueEventHandler {
  storeTaskListeners(taskId: string, listeners: ExtendedTaskEventListeners): void {
    super.storeTaskListeners(taskId, listeners);

    // 存储扩展的监听器
    if (listeners.onPause) {
      this.pauseListeners.set(taskId, listeners.onPause);
    }
    if (listeners.onResume) {
      this.resumeListeners.set(taskId, listeners.onResume);
    }
  }

  triggerPauseEvent(taskId: string): void {
    const listener = this.pauseListeners.get(taskId);
    if (listener) {
      listener(taskId);
    }
  }
}
```

### 5. 性能监控扩展

#### 自定义指标收集
```typescript
// monitoring/custom-metrics.ts
@Injectable()
export class CustomMetricsCollector {
  private readonly customMetrics = new Map<string, any>();

  @Cron('*/10 * * * * *') // 每10秒收集一次
  async collectMetrics(): Promise<void> {
    // 收集任务执行指标
    const executionMetrics = await this.collectExecutionMetrics();

    // 收集队列指标
    const queueMetrics = await this.collectQueueMetrics();

    // 收集节点指标
    const nodeMetrics = await this.collectNodeMetrics();

    // 发送到监控系统
    await this.sendToMonitoring({
      timestamp: new Date(),
      execution: executionMetrics,
      queue: queueMetrics,
      nodes: nodeMetrics
    });
  }

  private async collectExecutionMetrics(): Promise<ExecutionMetrics> {
    const stats = await this.taskExecutionService.getExecutionStats();

    return {
      totalTasks: stats.statusManager.totalTasks,
      completedTasks: stats.statusManager.completedTasks,
      failedTasks: stats.statusManager.failedTasks,
      averageExecutionTime: stats.statusManager.averageExecutionTime,
      throughput: stats.statusManager.tasksPerSecond
    };
  }
}
```

---

## 📝 总结

TaskModule的重构成功地应用了抽象思维，实现了以下目标：

### ✅ 重构成果

1. **代码质量提升**
   - 从300+行巨大服务拆分为40-120行的专门组件
   - 方法长度从30-50行优化为5-25行
   - 职责从8种混合优化为单一专门职责

2. **架构清晰化**
   - 四层清晰架构：协调层 → 专门组件层 → 抽象实现层 → 基础设施层
   - 单向依赖关系，避免循环依赖
   - 接口抽象，支持运行时替换实现

3. **可维护性增强**
   - 每个组件可独立测试和修改
   - 清晰的职责边界和调用链
   - 完整的文档和示例代码

4. **扩展性提升**
   - 支持新执行器、路由策略、状态处理器
   - 插件化架构，易于添加新功能
   - 配置驱动，支持运行时调整

### 🎯 设计模式应用

- **工厂模式**: TaskExecutorFactory根据任务类型创建执行器
- **策略模式**: 状态处理、节点选择、执行策略
- **委托模式**: 协调器委托给专门组件处理
- **模板方法**: 抽象执行器定义通用流程
- **依赖注入**: 接口抽象，松耦合设计

### 🚀 性能优化

- **数据库优化**: 索引优化、连接池配置、查询缓存
- **Redis优化**: 批量操作、管道技术、集群支持
- **内存优化**: LRU缓存、内存监控、垃圾回收
- **并发优化**: 信号量控制、批量处理、异步执行

这个重构项目展示了如何使用抽象思维将复杂的单体服务转换为清晰、可维护、高性能的模块化架构。每个组件都有明确的职责，代码简洁易懂，为后续的功能扩展和性能优化奠定了坚实的基础。